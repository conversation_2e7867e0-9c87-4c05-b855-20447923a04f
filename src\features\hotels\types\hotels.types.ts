// Base Hotel Interface
export interface Hotel {
  id: number;
  name: string;
  zone_id: number;
  zone_name?: string;
  address?: string;
  description?: string;
  amenities?: string[];
  rating?: number;
  image_url?: string;
  contact_info?: {
    phone?: string;
    email?: string;
    website?: string;
  };
  location?: {
    latitude?: number;
    longitude?: number;
  };
  created_at?: string;
  updated_at?: string;
  status?: 'active' | 'inactive';
}

// API Response Interfaces
export interface HotelResponse {
  success: boolean;
  total_hotels: number;
  hotels: Hotel[];
  message?: string;
}

export interface HotelByZoneResponse {
  success: boolean;
  zone_id: number;
  zone_name: string;
  total_hotels: number;
  hotels: Hotel[];
  message?: string;
}

// Request Parameters
export interface HotelsQueryParams {
  page?: number;
  limit?: number;
  zone_id?: number;
  search?: string;
  status?: 'active' | 'inactive' | 'all';
  sort_by?: 'name' | 'zone_name' | 'rating' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

export interface HotelsByZoneParams {
  zone_id: number;
  page?: number;
  limit?: number;
  search?: string;
  status?: 'active' | 'inactive' | 'all';
}

// Error Types
export interface HotelError {
  message: string;
  code?: string;
  field?: string;
  details?: Record<string, unknown>;
}

export interface HotelApiError {
  success: false;
  error: HotelError;
  status_code?: number;
}

// Hook Return Types
export interface UseHotelsResult {
  hotels: Hotel[];
  totalHotels: number;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  hasNextPage?: boolean;
  hasPreviousPage?: boolean;
  fetchNextPage?: () => void;
  fetchPreviousPage?: () => void;
}

export interface UseHotelsByZoneResult {
  hotels: Hotel[];
  totalHotels: number;
  zoneId: number;
  zoneName: string;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}
// Extended Error interface for API errors
export interface ApiError extends Error {
  status?: number;
  code?: string;
}

// API Hotel structure (as returned by the backend)
export interface ApiHotel {
  id_hotel: string;
  code?: string;
  nombre: string;
  address?: string;
  suburb?: string;
  zip_code?: string;
  city?: string;
  state?: string;
  country?: string;
  zones_idzones: string | number;
  zone_name?: string;
  source?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// API Response structure (raw from backend)
export interface ApiHotelResponse {
  success: boolean;
  total_hotels: number;
  hotels: ApiHotel[];
  message?: string;
}
