import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from 'lucide-react';

interface BackNavigationProps {
  onBack: () => void;
  label?: string;
}

const BackNavigation = ({ onBack, label = "Back to Services" }: BackNavigationProps) => {
  return (
    <Button 
      variant="ghost" 
      onClick={onBack}
      className="mb-6 text-muted-foreground hover:text-foreground"
    >
      <ArrowLeftIcon className="w-4 h-4 mr-2" />
      {label}
    </Button>
  );
};

export default BackNavigation;
