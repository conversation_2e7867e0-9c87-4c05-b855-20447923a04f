export interface ServiceFleets {
    id_fleet:      string;
    id_code:       string;
    name:          string;
    name_esp:      string;
    shortname:     string;
    description:   string;
    activo:        string;
    passengers:    string;
    luggage:       string;
    imgurl:        string;
    imgurl_grande: string;
    service:       string;
    vehicles:      string;
    paypal_item:   string;
    services:      Service[];
}

export interface Service {
    id_service:   string;
    service_name: ServiceName;
    service_type: ServiceType;
}

export enum ServiceName {
    OneWayAirport = "One Way Airport",
    RoundTripAirport = "Round Trip Airport",
}

export enum ServiceType {
    Private = "Private",
    Shared = "Shared",
}
