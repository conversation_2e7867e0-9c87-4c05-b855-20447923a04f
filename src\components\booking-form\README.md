# Booking Form Components

Este directorio contiene todos los componentes relacionados con formularios de reserva, refactorizados siguiendo el principio DRY (Don't Repeat Yourself).

## ✨ Componente Unificado (Recomendado)

### BookingForm

**Descripción**: Componente único con soporte para múltiples variantes (enhanced, vertical, horizontal)

**Características**:
- ✅ **Diseño flexible** con 3 variantes: `enhanced`, `vertical`, `horizontal`
- ✅ **Props configurables** para mostrar/ocultar secciones (contacto, ofertas especiales)
- ✅ **Auto-navegación controlable** 
- ✅ **Reutilizable** con diferentes configuraciones
- ✅ **Mantiene toda la funcionalidad** de los componentes anteriores

**Uso básico**:
```tsx
import { BookingForm } from '@/components/booking-form';

// Variante Enhanced (sidebar, sin navegación automática)
<BookingForm 
  variant="enhanced"
  onFormChange={handleFormChange}
  initialData={formData}
  autoNavigate={false}
  showContactInfo={true}
  showSpecialOffer={true}
/>

// Variante Vertical (página principal, navegación automática)
<BookingForm 
  variant="vertical"
  autoNavigate={true}
  showContactInfo={false}
  showSpecialOffer={false}
/>

// Variante Horizontal (hero section, navegación automática)
<BookingForm 
  variant="horizontal"
  autoNavigate={true}
  showContactInfo={false}
  showSpecialOffer={false}
/>
```

**Props del BookingForm**:
- `variant`: `'enhanced' | 'vertical' | 'horizontal'` - Tipo de diseño
- `onFormChange`: Callback cuando cambian los datos del formulario
- `initialData`: Datos iniciales para pre-popular el formulario
- `showContactInfo`: Mostrar información de contacto (solo enhanced)
- `showSpecialOffer`: Mostrar banner de ofertas especiales (solo enhanced)
- `autoNavigate`: Si debe navegar automáticamente al enviar
- `className`: Clases CSS adicionales

---

## 📋 Componentes Legados (Para Compatibilidad)

## 🏗️ Arquitectura

### Componentes Principales

#### BookingFormEnhanced
- **Archivo**: `BookingFormEnhanced.tsx`
- **Descripción**: Versión completa con diseño de tarjeta, contacto y ofertas especiales
- **Características**:
  - Card layout con gradiente
  - Campos separados para adultos y niños
  - Información de contacto
  - Banner de ofertas especiales
  - Callbacks para manejo externo de datos

#### BookingFormHorizontal
- **Archivo**: `BookingFormHorizontal.tsx`
- **Descripción**: Layout horizontal optimizado para espacios amplios
- **Características**:
  - Layout de 5 columnas con botón inline
  - Navegación automática a página de reserva
  - Diseño con background y sombras
  - Información detallada del aeropuerto seleccionado

#### BookingFormVertical
- **Archivo**: `BookingFormVertical.tsx`
- **Descripción**: Layout vertical para espacios más estrechos
- **Características**:
  - Layout responsivo vertical
  - Campo único de "passengers" en lugar de adults/kids
  - Optimizado para móviles
  - Navegación incluida

### Componentes Compartidos

Todos los campos del formulario han sido extraídos en componentes reutilizables:

- **OriginSelect**: Selector de origen (aeropuertos)
- **DestinationSearch**: Búsqueda de destino (hoteles/direcciones)
- **DatePicker**: Selector de fecha
- **TimeSelect**: Selector de hora
- **PassengerSelect**: Selector de pasajeros (adults, kids, o total)
- **RoundTripToggle**: Toggle para viaje redondo

### Hook Compartido

**useBookingForm**: Maneja toda la lógica común:
- Estado del formulario
- Validaciones
- Handlers de eventos
- Lógica de envío
- Analytics
- Navegación

## 📝 Uso

### ⭐ Método Recomendado - Componente Unificado
```tsx
import { BookingForm } from '@/components/booking-form';

// Para página de booking (sidebar)
<BookingForm 
  variant="enhanced"
  onFormChange={handleFormChange}
  autoNavigate={false}
  showContactInfo={true}
  showSpecialOffer={true}
/>

// Para página principal (hero)
<BookingForm 
  variant="vertical"
  autoNavigate={true}
/>

// Para layout horizontal
<BookingForm 
  variant="horizontal"
  autoNavigate={true}
/>
```

### 🔄 Migración desde Componentes Legados

**Antes**:
```tsx
import { BookingFormEnhanced, BookingFormVertical } from '@/components/booking-form';

<BookingFormEnhanced onFormChange={handleChange} initialData={data} />
<BookingFormVertical />
```

**Después**:
```tsx
import { BookingForm } from '@/components/booking-form';

<BookingForm 
  variant="enhanced" 
  onFormChange={handleChange} 
  initialData={data}
  autoNavigate={false}
/>
<BookingForm variant="vertical" autoNavigate={true} />
```

### 💾 Importación Legacy (Para Compatibilidad)
```tsx
import { 
  BookingFormEnhanced,
  BookingFormHorizontal,
  BookingFormVertical
} from '@/components/booking-form';

// Uso directo (funciona igual que antes)
<BookingFormHorizontal />
<BookingFormVertical />
<BookingFormEnhanced onFormChange={handleChange} />
```

### Uso Avanzado con Hook
```tsx
import { useBookingForm, OriginSelect, DatePicker } from '@/components/booking-form';

const CustomForm = () => {
  const { formState, handlers, airportData } = useBookingForm({
    formType: 'custom',
    onFormChange: (data) => console.log(data)
  });

  return (
    <form onSubmit={handlers.handleSubmit}>
      <OriginSelect
        value={formState.from}
        onChange={(value) => handlers.updateField('from', value)}
        airportOptions={airportData.airportOptions}
      />
      <DatePicker
        value={formState.date}
        onChange={(date) => handlers.updateField('date', date)}
      />
    </form>
  );
};
```

## 🔧 Beneficios de la Refactorización

### Eliminación de Duplicación
- ✅ **800+ líneas reducidas** a componentes reutilizables
- ✅ **Lógica centralizada** en el hook `useBookingForm`
- ✅ **Componentes de campo** reutilizables entre formularios

### Mantenibilidad
- ✅ **Un solo lugar** para cambios de validación
- ✅ **Componentes modulares** fáciles de testear
- ✅ **API consistente** entre todos los formularios

### Flexibilidad
- ✅ **Hook personalizable** para diferentes tipos de formulario
- ✅ **Componentes independientes** que se pueden usar por separado
- ✅ **Props configurables** para diferentes casos de uso

## 📁 Estructura de Archivos

```
src/components/booking-form/
├── BookingForm.tsx             (componente unificado - RECOMENDADO)
├── hooks/
│   └── useBookingForm.ts       (lógica compartida)
├── components/
│   ├── OriginSelect.tsx
│   ├── DestinationSearch.tsx
│   ├── DatePicker.tsx
│   ├── TimeSelect.tsx
│   ├── PassengerSelect.tsx
│   ├── RoundTripToggle.tsx
│   └── index.ts
├── legacy/                     (componentes obsoletos)
│   ├── BookingFormEnhanced.tsx     (DEPRECATED)
│   ├── BookingFormHorizontal.tsx   (DEPRECATED)
│   ├── BookingFormVertical.tsx     (DEPRECATED)
│   └── README.md               (guía de migración)
├── types.ts                    (tipos compartidos)
├── index.ts                    (exports centralizados)
└── README.md                   (documentación)
```

## 🎯 Tipos

### BookingFormData
```tsx
interface BookingFormData {
  from: string;
  to: string;
  date: Date | null;
  time: string;
  adults: string;
  kids: string;
  passengers: string;
  roundTrip: boolean;
}
```

### UseBookingFormProps
```tsx
interface UseBookingFormProps {
  onFormChange?: (data: BookingFormData) => void;
  initialData?: Partial<BookingFormData>;
  formType?: 'horizontal' | 'vertical' | 'enhanced';
}
```

La refactorización mantiene toda la funcionalidad original mientras elimina la duplicación de código y mejora significativamente la mantenibilidad del proyecto.
