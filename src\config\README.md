
# Sistema de Configuración de Temas - Multibranding

Este sistema permite cambiar fácilmente colores, logotipos y información de la empresa para crear múltiples marcas del mismo sitio web.

## Estructura

### `theme.ts`
Contiene las configuraciones de tema con:
- **Colores**: Primary, secondary, accent, background, surface
- **Gradientes**: Primary y secondary
- **Logotipos**: Main, footer (opcional), alt text
- **Información de empresa**: Name, phone, email, address

### `useTheme.tsx`
Hook personalizado que proporciona:
- Acceso fácil a la configuración del tema activo
- Generación de variables CSS
- Helpers para colores, gradientes, logos y datos de empresa

## Cómo usar

### 1. Cambiar de marca/tema
En `src/config/theme.ts`, cambia la línea:
```typescript
export const activeTheme = bajaTheme;
```
Por:
```typescript
export const activeTheme = alternativeTheme;
```

### 2. Crear nuevo tema
```typescript
export const nuevoTema: ThemeConfig = {
  name: 'Nuevo Nombre',
  colors: {
    primary: '#123456',
    secondary: '#654321',
    accent: '#ABCDEF',
    background: '#FFFFFF',
    surface: '#F9FAFB',
  },
  gradients: {
    primary: 'linear-gradient(135deg, #123456 0%, #**********%)',
    secondary: 'linear-gradient(45deg, #ABCDEF 0%, #**********%)',
  },
  logos: {
    main: '/path/to/new-logo.png',
    alt: 'Nuevo Nombre',
  },
  company: {
    name: 'Nuevo Nombre',
    phone: '+52 XXX XXX-XXXX',
    email: '<EMAIL>',
    address: 'Nueva Dirección',
  },
};
```

### 3. Usar en componentes
```typescript
import { useTheme } from '@/shared/hooks/theme';

const MiComponente = () => {
  const { colors, logos, company } = useTheme();
  
  return (
    <div style={{ backgroundColor: colors.primary }}>
      <img src={logos.main} alt={company.name} />
      <h1>{company.name}</h1>
    </div>
  );
};
```

## Ventajas

1. **Fácil multibranding**: Cambio de tema en una sola línea
2. **Consistencia**: Todos los componentes usan la misma fuente de verdad
3. **Mantenimiento**: Cambios centralizados
4. **Escalabilidad**: Fácil agregar nuevos temas
5. **TypeScript**: Tipado fuerte para evitar errores

## Archivos actualizados

- `Header.tsx`: Usa logos y datos de empresa del tema
- `Footer.tsx`: Usa logos y datos de empresa del tema
- Más componentes pueden ser actualizados siguiendo el mismo patrón
