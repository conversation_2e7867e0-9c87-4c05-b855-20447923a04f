export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
  };
  gradients: {
    primary: string;
    secondary: string;
  };
  logos: {
    main: string;
    footer?: string;
    alt: string;
  };
  company: {
    name: string;
    phone: string;
    email: string;
    address: string;
  };
  bookingLayout: {
    defaultLayout: 'vertical-right' | 'horizontal-below';
  };
}

// Configuración actual de Baja Travel Transportation
export const bajaTheme: ThemeConfig = {
  name: 'Baja Travel Transportation',
  colors: {
    primary: '#006498', // Azul marino
    secondary: '#13b6c6', // Azul turquesa
    accent: '#F4E4BC', // Arena/Sand
    background: '#FFFFFF',
    surface: '#F9FAFB',
  },
  gradients: {
    primary: 'linear-gradient(135deg, #006498 0%, #13b6c6 100%)',
    secondary: 'linear-gradient(45deg, #F4E4BC 0%, #13b6c6 100%)',
  },
  logos: {
    main: '/lovable-uploads/57c2c2d5-d0ce-4bb9-9c02-a9e15cb50fa6.png',
    footer: '/lovable-uploads/57c2c2d5-d0ce-4bb9-9c02-a9e15cb50fa6.png',
    alt: 'Baja Travel Transportation',
  },
  company: {
    name: 'Baja Travel Transportation',
    phone: '+52 ************',
    email: '<EMAIL>',
    address: 'Los Cabos SJD Airport, Baja California Sur',
  },
  bookingLayout: {
    defaultLayout: 'horizontal-below',
  },
};

// Ejemplo de tema alternativo para multibranding
export const alternativeTheme: ThemeConfig = {
  name: 'Cabo Premium Transfer',
  colors: {
    primary: '#1E40AF', // Azul diferente
    secondary: '#059669', // Verde
    accent: '#F59E0B', // Amarillo/Dorado
    background: '#FFFFFF',
    surface: '#F9FAFB',
  },
  gradients: {
    primary: 'linear-gradient(135deg, #1E40AF 0%, #**********%)',
    secondary: 'linear-gradient(45deg, #F59E0B 0%, #**********%)',
  },
  logos: {
    main: '/path/to/alternative-logo.png',
    alt: 'Cabo Premium Transfer',
  },
  company: {
    name: 'Cabo Premium Transfer',
    phone: '+52 ************',
    email: '<EMAIL>',
    address: 'Los Cabos International Airport',
  },
  bookingLayout: {
    defaultLayout: 'horizontal-below',
  },
};

// Tema activo - cambiar aquí para switch entre marcas
export const activeTheme = bajaTheme;

// Utilidad para generar CSS custom properties
export const generateCSSVariables = (theme: ThemeConfig) => {
  return {
    '--theme-primary': theme.colors.primary,
    '--theme-secondary': theme.colors.secondary,
    '--theme-accent': theme.colors.accent,
    '--theme-background': theme.colors.background,
    '--theme-surface': theme.colors.surface,
    '--theme-gradient-primary': theme.gradients.primary,
    '--theme-gradient-secondary': theme.gradients.secondary,
  };
};
