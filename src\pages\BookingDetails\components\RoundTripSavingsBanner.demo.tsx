import { Card, CardContent } from '@/components/ui/card';
import { CheckCircleIcon } from 'lucide-react';

/**
 * Componente de demostración del banner de descuento Round Trip
 * Muestra cómo se vería el banner con datos de ejemplo
 */
const RoundTripSavingsBannerDemo = () => {
  // Datos de ejemplo para demostración
  const exampleSavings = {
    oneWayPrice: 73,
    twoOneWayPrice: 146,
    roundTripPrice: 132,
    savings: 14,
    savingsPercentage: 10
  };

  return (
    <div className="space-y-4 p-4">
      <h3 className="text-lg font-semibold">Round Trip Savings Banner Demo</h3>
      
      {/* Banner de descuento */}
      <Card className="border-2 border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 shadow-sm">
        <CardContent className="p-3">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircleIcon className="w-4 h-4 text-green-600" />
            <span className="text-green-700 font-semibold text-sm">
              Best Value
            </span>
          </div>
          
          <div className="space-y-1">
            <div className="text-sm text-gray-700">
              <span className="text-gray-600">One way price:</span>
              <span className="ml-2 line-through text-gray-500 font-medium">
                ${exampleSavings.twoOneWayPrice.toFixed(2)}
              </span>
            </div>
            
            <div className="text-sm font-medium">
              <span className="text-green-600">
                Save ${exampleSavings.savings.toFixed(2)} ({exampleSavings.savingsPercentage}% off)
              </span>
              <span className="text-gray-600 text-xs block mt-0.5">
                vs. booking two One-Way trips
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Explicación de los cálculos */}
      <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
        <h4 className="font-medium mb-2">Cálculo del descuento:</h4>
        <ul className="space-y-1">
          <li>• Precio One Way: ${exampleSavings.oneWayPrice}</li>
          <li>• Dos viajes One Way: ${exampleSavings.oneWayPrice} × 2 = ${exampleSavings.twoOneWayPrice}</li>
          <li>• Precio Round Trip: ${exampleSavings.roundTripPrice}</li>
          <li>• Ahorro: ${exampleSavings.twoOneWayPrice} - ${exampleSavings.roundTripPrice} = ${exampleSavings.savings}</li>
          <li>• Porcentaje: ({exampleSavings.savings} ÷ ${exampleSavings.twoOneWayPrice}) × 100 = {exampleSavings.savingsPercentage}%</li>
        </ul>
      </div>

      {/* Condiciones para mostrar el banner */}
      <div className="text-sm text-blue-600 bg-blue-50 p-3 rounded-lg">
        <h4 className="font-medium mb-2">El banner se muestra cuando:</h4>
        <ul className="space-y-1">
          <li>• El viaje es Round Trip</li>
          <li>• Hay ahorro mayor a $5</li>
          <li>• El porcentaje de descuento es mayor a 0%</li>
          <li>• Se tienen datos de tarifas válidos</li>
        </ul>
      </div>
    </div>
  );
};

export default RoundTripSavingsBannerDemo;
