import { API_ENDPOINTS, ERROR_MESSAGES } from '../../../config/config';
import type { ServiceFleets } from '../types/service-fleets.types';

export async function fetchDetailedFleets(): Promise<ServiceFleets[]> {

//   console.log(API_ENDPOINTS.FLEETS.DETAILED)
  const baseUrl = API_ENDPOINTS.FLEETS.DETAILED;

  try {
    const response = await fetch(baseUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(ERROR_MESSAGES?.SERVER || 'Error fetching fleets');
    }

    const data = await response.json();
    return data as ServiceFleets[];
  } catch (error: unknown) {
    // Optionally log error or handle it differently
    if (error instanceof Error) {
      throw new Error(error.message || ERROR_MESSAGES?.UNKNOWN || 'Unknown error');
    }
    throw new Error(ERROR_MESSAGES?.UNKNOWN || 'Unknown error');
  }
}

