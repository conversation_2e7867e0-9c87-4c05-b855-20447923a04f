import React, { useState } from 'react';
import {
  useHotels,
  useHotelsByZone,
  useHotelsSearch,
  usePrefetchHotels,
  useHotelsCache,
} from '../hooks/useHotels';
import type { HotelsQueryParams, Hotel } from '../types/hotels.types';

/**
 * Example component demonstrating the complete Hotels feature implementation
 */
export function HotelsExample() {
  const [selectedZoneId, setSelectedZoneId] = useState<number>(1);
  const [currentPage, setCurrentPage] = useState<number>(1);

  // Example 1: Basic hotels list with pagination
  const hotelsParams: HotelsQueryParams = {
    page: currentPage,
    limit: 10,
    status: 'active',
    sort_by: 'name',
    sort_order: 'asc',
  };

  const {
    hotels,
    totalHotels,
    isLoading,
    isError,
    error,
    refetch,
    hasNextPage,
    hasPreviousPage,
  } = useHotels(hotelsParams);

  // Example 2: Hotels by zone
  const {
    hotels: zoneHotels,
    totalHotels: zoneTotalHotels,
    zoneName,
    isLoading: isZoneLoading,
    isError: isZoneError,
    error: zoneError,
  } = useHotelsByZone({ zone_id: selectedZoneId });

  // Example 3: Hotels search with debouncing
  const {
    hotels: searchHotels,
    searchParams,
    updateSearch,
    resetSearch,
    isSearching,
    isLoading: isSearchLoading,
  } = useHotelsSearch({ status: 'active' }, 300);

  // Example 4: Prefetch and cache management
  const { prefetchAllHotels, prefetchHotelsByZone } = usePrefetchHotels();
  const { invalidateAllHotels, removeHotelsCache } = useHotelsCache();

  // Event handlers
  const handleZoneChange = (zoneId: number) => {
    setSelectedZoneId(zoneId);
    // Prefetch data when zone changes
    prefetchHotelsByZone({ zone_id: zoneId });
  };

  const handleSearchChange = (search: string) => {
    updateSearch({ search, page: 1 });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRefreshData = () => {
    invalidateAllHotels();
    refetch();
  };

  const handleClearCache = () => {
    removeHotelsCache();
  };

  if (isLoading && currentPage === 1) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading hotels...</div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-red-600 p-4">
        <h3 className="font-semibold">Error loading hotels:</h3>
        <p>{error?.message}</p>
        <button
          onClick={() => refetch()}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Hotels Management Example</h1>

      {/* Control Panel */}
      <div className="mb-6 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Controls</h2>
        <div className="flex gap-4 flex-wrap">
          <button
            onClick={handleRefreshData}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh Data
          </button>
          <button
            onClick={handleClearCache}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Clear Cache
          </button>
          <button
            onClick={() => prefetchAllHotels({ page: currentPage + 1 })}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Prefetch Next Page
          </button>
        </div>
      </div>

      {/* Search Section */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Search Hotels</h2>
        <div className="flex gap-4">
          <input
            type="text"
            placeholder="Search hotels..."
            value={searchParams.search || ''}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="flex-1 px-4 py-2 border rounded-lg"
          />
          <button
            onClick={resetSearch}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Reset
          </button>
        </div>
        {isSearching && (
          <div className="mt-2 text-sm text-blue-600">Searching...</div>
        )}
        
        {/* Search Results */}
        {searchParams.search && (
          <div className="mt-4">
            <h3 className="font-semibold mb-2">
              Search Results ({searchHotels.length} hotels found)
            </h3>
            {isSearchLoading ? (
              <div>Loading search results...</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {searchHotels.map((hotel) => (
                  <HotelCard key={hotel.id} hotel={hotel} />
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Zone Filter Section */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Hotels by Zone</h2>
        <div className="flex gap-2 mb-4">
          {[1, 2, 3, 4, 5].map((zoneId) => (
            <button
              key={zoneId}
              onClick={() => handleZoneChange(zoneId)}
              className={`px-4 py-2 rounded ${
                selectedZoneId === zoneId
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 hover:bg-gray-300'
              }`}
            >
              Zone {zoneId}
            </button>
          ))}
        </div>
        
        {isZoneLoading ? (
          <div>Loading zone hotels...</div>
        ) : isZoneError ? (
          <div className="text-red-600">
            Error loading zone hotels: {zoneError?.message}
          </div>
        ) : (
          <div>
            <h3 className="font-semibold mb-2">
              {zoneName} ({zoneTotalHotels} hotels)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {zoneHotels.map((hotel) => (
                <HotelCard key={hotel.id} hotel={hotel} />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* All Hotels Section */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">
          All Hotels ({totalHotels} total)
        </h2>
        
        {/* Pagination Controls */}
        <div className="flex justify-between items-center mb-4">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={!hasPreviousPage}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
          >
            Previous
          </button>
          <span>Page {currentPage}</span>
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={!hasNextPage}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
          >
            Next
          </button>
        </div>

        {/* Hotels Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {hotels.map((hotel) => (
            <HotelCard key={hotel.id} hotel={hotel} />
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * Hotel Card Component
 */
function HotelCard({ hotel }: { hotel: Hotel }) {
  return (
    <div className="border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
      <h3 className="font-semibold text-lg mb-2">{hotel.name}</h3>
      <p className="text-gray-600 mb-2">Zone: {hotel.zone_name || `Zone ${hotel.zone_id}`}</p>
      {hotel.address && (
        <p className="text-sm text-gray-500 mb-2">{hotel.address}</p>
      )}
      {hotel.rating && (
        <div className="flex items-center mb-2">
          <span className="text-yellow-500">★</span>
          <span className="ml-1">{hotel.rating}/5</span>
        </div>
      )}
      {hotel.amenities && hotel.amenities.length > 0 && (
        <div className="mb-2">
          <p className="text-sm font-medium">Amenities:</p>
          <div className="flex flex-wrap gap-1">
            {hotel.amenities.slice(0, 3).map((amenity, index) => (
              <span
                key={index}
                className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded"
              >
                {amenity}
              </span>
            ))}
            {hotel.amenities.length > 3 && (
              <span className="text-xs text-gray-500">
                +{hotel.amenities.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}
      <div className="flex justify-between items-center text-sm text-gray-500">
        <span>ID: {hotel.id}</span>
        <span className={hotel.status === 'active' ? 'text-green-600' : 'text-red-600'}>
          {hotel.status || 'Active'}
        </span>
      </div>
    </div>
  );
}

export default HotelsExample;
