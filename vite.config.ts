import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    proxy: {
      // Proxiamos la API para evitar CORS en desarrollo
      '/api': {
        target: 'http://localhost/bajatravel',
        changeOrigin: true,
        secure: false,
        // Reescribe /api -> /api para mantener prefijo
        // Si tu backend espera /bajatravel/api, este target ya lo incluye
        // por lo que no necesitamos rewrite aquí.
        // En caso de mover, podrías usar:
        // rewrite: (path) => path.replace(/^\/api/, '/api')
      },
    },
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
