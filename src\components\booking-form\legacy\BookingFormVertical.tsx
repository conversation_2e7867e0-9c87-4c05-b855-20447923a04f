
import { Button } from '@/components/ui/button';
import { useBooking } from '@/context/BookingContext';
import type { Hotel } from '@/features/hotels';
import {
  OriginSelect,
  DestinationSearch,
  DatePicker,
  TimeSelect,
  PassengerSelect,
  RoundTripToggle
} from '../components';

const BookingFormVertical = () => {
  const { state, updateFormData, setSelectedHotel, setRoundTrip } = useBooking();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Implementar lógica de envío si es necesaria
  };

  return (
    <form className="space-y-6" onSubmit={handleSubmit}>
      {/* Header with title and round trip toggle */}
      <div className="flex justify-between items-center">
        <h3 className="text-2xl font-bold text-gray-900">
          Book Your Transfer
        </h3>
        
        <RoundTripToggle
          value={state.roundTrip}
          onChange={setRoundTrip}
        />
      </div>

      {/* Vertical Form Fields */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <OriginSelect
            value={state.from}
            onChange={(value) => updateFormData({ from: value })}
            error={false}
            airportOptions={[
              { value: "sjd", label: "SJD Airport", city: "Los Cabos" },
              { value: "hotel", label: "Hotel", city: "Pick up" }
            ]}
            errorText="Please select origin"
          />
          
          <DestinationSearch
            value={state.to}
            onChange={(value, hotel) => {
              updateFormData({ to: value });
              if (hotel) {
                setSelectedHotel(hotel);
              }
            }}
            error={false}
            errorText="Please enter destination"
            selectedHotel={state.selectedHotel}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <DatePicker
            value={state.date}
            onChange={(date) => updateFormData({ date })}
            error={false}
            errorText="Please select date"
          />
          
          <TimeSelect
            value={state.time}
            onChange={(value) => updateFormData({ time: value })}
            error={false}
            errorText="Please select time"
          />

          <PassengerSelect
            value={state.adults}
            onChange={(value) => updateFormData({ adults: value })}
            error={false}
            type="total"
            errorText="Please select passengers"
          />
        </div>
      </div>

      <div className="text-center">
        <Button 
          type="submit"
          className="bg-gradient-cabo hover:opacity-90 text-white font-semibold py-3 px-12 text-lg"
        >
          Book Now
        </Button>
      </div>
    </form>
  );
};

export default BookingFormVertical;
