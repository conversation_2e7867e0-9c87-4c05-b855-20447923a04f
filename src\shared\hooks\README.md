# Shared Hooks

Esta carpeta contiene todos los hooks reutilizables de la aplicación, organizados por categoría.

## Estructura

```
src/shared/hooks/
├── ui/                 # Hooks relacionados con la interfaz de usuario
│   ├── use-mobile.ts   # Detección de dispositivos móviles y responsive
│   ├── use-toast.ts    # Sistema de notificaciones toast
│   └── index.ts        # Exportaciones de UI hooks
├── analytics/          # Hooks para tracking y analytics
│   ├── use-analytics.ts # Google Analytics y GTM
│   └── index.ts        # Exportaciones de analytics hooks
├── theme/             # Hooks para manejo de temas
│   ├── use-theme.ts   # Configuración y estilos del tema
│   └── index.ts       # Exportaciones de theme hooks
└── index.ts           # Exportaciones principales
```

## Hooks Disponibles

### UI Hooks

#### `useIsMobile()`
Detecta si el dispositivo es móvil basado en el ancho de pantalla.
```typescript
const isMobile = useIsMobile();
```

#### `useViewport()`
Obtiene las dimensiones actuales del viewport.
```typescript
const { width, height, isMobile } = useViewport();
```

#### `useBreakpoints()`
Detecta diferentes breakpoints de pantalla.
```typescript
const { isMobile, isTablet, isDesktop, isLarge } = useBreakpoints();
```

#### `useToast()`
Sistema de notificaciones toast mejorado.
```typescript
const { toast, dismiss } = useToast();

// Usar variantes predefinidas
toastVariants.success('Operación exitosa');
toastVariants.error('Error al procesar');
```

### Analytics Hooks

#### `useAnalytics()`
Hook completo para tracking de eventos y análisis.
```typescript
const { 
  trackEvent, 
  trackPageView, 
  trackBookingStart,
  trackBookingComplete 
} = useAnalytics();

// Tracking de eventos personalizados
trackEvent({
  action: 'button_click',
  category: 'engagement',
  label: 'hero_cta',
  value: 1
});

// Tracking de vistas de página
trackPageView({
  pagePath: '/destinations/cabo',
  pageTitle: 'Los Cabos Transportation'
});
```

### Theme Hooks

#### `useTheme()`
Hook principal para manejo del tema de la aplicación.
```typescript
const { 
  theme, 
  colors, 
  gradients, 
  applyThemeStyles 
} = useTheme();
```

#### `useThemeColors()`
Acceso rápido a los colores del tema.
```typescript
const { 
  primary, 
  secondary, 
  accent, 
  getPrimaryColor 
} = useThemeColors();
```

#### `useThemeGradients()`
Utilitarios para gradientes del tema.
```typescript
const { 
  primary: primaryGradient, 
  createGradient 
} = useThemeGradients();
```

## Migración

### Desde hooks anteriores:
```typescript
// ❌ Antiguo
import useAnalytics from '@/hooks/useAnalytics';
import { useIsMobile } from '@/hooks/use-mobile';
import useTheme from '@/hooks/useTheme';

// ✅ Nuevo
import { useAnalytics } from '@/shared/hooks/analytics';
import { useIsMobile } from '@/shared/hooks/ui';
import { useTheme } from '@/shared/hooks/theme';

// ✅ O desde el índice principal
import { useAnalytics, useIsMobile, useTheme } from '@/shared/hooks';
```

### Cambios en APIs:

#### Analytics
```typescript
// ❌ Antiguo
trackPageView('/page', 'Title');

// ✅ Nuevo
trackPageView({ 
  pagePath: '/page', 
  pageTitle: 'Title' 
});
```

## Best Practices

1. **Importaciones**: Usa las importaciones desde los índices para mejor tree-shaking
2. **TypeScript**: Todos los hooks incluyen tipado completo
3. **Performance**: Los hooks están optimizados con `useCallback` y `useMemo`
4. **Error Handling**: Incluyen manejo de errores y casos edge
5. **SSR**: Compatible con renderizado del lado del servidor

## Ejemplos de Uso

Ver los archivos de ejemplo en cada subcarpeta para implementaciones específicas.
