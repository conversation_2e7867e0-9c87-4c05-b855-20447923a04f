import { 
  AirportsListExample,
  AirportSearchExample, 
  AirportsWithLocalSearchExample,
  AirportsWithCustomOptionsExample 
} from '@/features/airports/examples/AirportsExamples';

const TestAirportsPage = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 space-y-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🛩️ Airports React Query Examples
          </h1>
          <p className="text-lg text-gray-600">
            Ejemplos de uso de los hooks refactorizados con React Query
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Ejemplo 1 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-blue-600">
              📋 Hook Básico - useAirports()
            </h2>
            <AirportsListExample />
          </div>

          {/* Ejemplo 2 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-green-600">
              🔍 Hook de Búsqueda - useAirportSearch()
            </h2>
            <AirportSearchExample />
          </div>

          {/* Ejemplo 3 */}
          <div className="bg-white rounded-lg shadow-md p-6 lg:col-span-2">
            <h2 className="text-xl font-semibold mb-4 text-purple-600">
              ⚡ Hook Optimizado - useAirportsWithLocalSearch()
            </h2>
            <AirportsWithLocalSearchExample />
          </div>

          {/* Ejemplo 4 */}
          <div className="bg-white rounded-lg shadow-md p-6 lg:col-span-2">
            <h2 className="text-xl font-semibold mb-4 text-orange-600">
              ⚙️ Hook con Opciones Personalizadas
            </h2>
            <AirportsWithCustomOptionsExample />
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">
            💡 Información sobre esta página
          </h3>
          <p className="text-blue-700 mb-4">
            Esta página es temporal y muestra ejemplos de uso de los hooks refactorizados con React Query.
          </p>
          <div className="space-y-2 text-sm text-blue-600">
            <p>• <strong>Cache:</strong> Los datos se cachean automáticamente por 5 minutos</p>
            <p>• <strong>Background Updates:</strong> Los datos se actualizan en segundo plano</p>
            <p>• <strong>Error Handling:</strong> Retry automático en caso de fallos</p>
            <p>• <strong>Loading States:</strong> Estados granulares de carga</p>
          </div>
        </div>

        <div className="text-center">
          <a 
            href="/" 
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            ← Volver al inicio
          </a>
        </div>
      </div>
    </div>
  );
};

export default TestAirportsPage;
