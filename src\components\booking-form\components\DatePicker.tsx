import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface DatePickerProps {
  value?: Date;
  onChange: (date: Date | undefined) => void;
  error?: boolean;
  className?: string;
  placeholder?: string;
  errorText?: string;
  label?: string;
  required?: boolean;
  minDate?: Date;
}

export const DatePicker = ({
  value,
  onChange,
  error,
  className,
  placeholder = "Select date",
  errorText,
  label = "Date",
  minDate
}: DatePickerProps) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return (
    <div className="w-full">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full h-12 justify-start text-left font-normal",
              !value && "text-muted-foreground",
              error && "border-red-500",
              className
            )}
          >
            {value ? format(value, "MM/dd/yyyy") : placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 bg-white z-50" align="start">
          <Calendar
            mode="single"
            selected={value}
            onSelect={onChange}
            initialFocus
            className="pointer-events-auto"
            disabled={(date) => date < (minDate || today)}
            fromDate={minDate || today}
          />
        </PopoverContent>
      </Popover>
      {/* Reserve space for error message to prevent layout shift */}
      <div className="h-5 mt-1">
        {error && <p className="text-red-500 text-xs">{errorText || "Required"}</p>}
      </div>
    </div>
  );
};
