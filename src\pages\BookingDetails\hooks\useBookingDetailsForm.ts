import { useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useCallback } from 'react';
import { useBooking } from '@/context/BookingContext';
import { useAnalytics } from '@/shared/hooks/analytics';
import {
  bookingDetailsSchema,
  defaultBookingDetailsData,
  type BookingDetailsFormData,
  type ContactInfoFormData,
  type FlightInfoFormData,
  type AdditionalItemsFormData
} from '../schemas/bookingDetailsSchema';

export const useBookingDetailsForm = () => {
  const {
    state,
    setContactInfo,
    setFlightInfo,
    setAdditionalItems,
    setBookingComplete,
    getCompleteBookingData
  } = useBooking();
  const { trackEvent } = useAnalytics();

  // Inicializar formulario con React Hook Form
  const form = useForm<BookingDetailsFormData>({
    resolver: zodResolver(bookingDetailsSchema),
    defaultValues: {
      ...defaultBookingDetailsData,
      // Cargar datos existentes del contexto si existen
      contactInfo: state.contactInfo || defaultBookingDetailsData.contactInfo,
      flightInfo: state.flightInfo || defaultBookingDetailsData.flightInfo,
      additionalItems: state.additionalItems || defaultBookingDetailsData.additionalItems
    },
    mode: 'onChange' // Validación en tiempo real
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isDirty },
    setValue,
    getValues,
    watch,
    reset
  } = form;

  // Watch para cambios en tiempo real
  const watchedData = useWatch({ control });

  // Sincronizar cambios del formulario con el contexto
  useEffect(() => {
    if (isDirty && watchedData && isValid) {
      // Actualizar contexto solo si los datos son válidos
      if (watchedData.contactInfo) {
        setContactInfo(watchedData.contactInfo as any); // Type assertion temporal
      }
      if (watchedData.flightInfo) {
        setFlightInfo(watchedData.flightInfo as any); // Type assertion temporal
      }
      if (watchedData.additionalItems) {
        setAdditionalItems(watchedData.additionalItems as any); // Type assertion temporal
      }

      // Track form changes
      trackEvent({
        action: 'booking_details_form_change',
        category: 'Booking Details',
        label: 'Form Updated'
      });
    }
  }, [watchedData, isDirty, isValid, setContactInfo, setFlightInfo, setAdditionalItems, trackEvent]);

  // Función para enviar el formulario
  const onSubmit = useCallback((data: BookingDetailsFormData) => {
    // Actualizar contexto con datos finales
    setContactInfo(data.contactInfo as any);
    setFlightInfo(data.flightInfo as any);
    setAdditionalItems(data.additionalItems as any);
    setBookingComplete(true);

    // Track successful submission
    trackEvent({
      action: 'booking_details_completed',
      category: 'Booking Details',
      label: 'Form Submitted Successfully'
    });

    // Obtener datos completos para envío a base de datos
    const completeBookingData = getCompleteBookingData();

    console.log('Complete booking data ready for database:', completeBookingData);

    return completeBookingData;
  }, [setContactInfo, setFlightInfo, setAdditionalItems, setBookingComplete, trackEvent, getCompleteBookingData]);

  // Función para resetear el formulario
  const resetForm = useCallback(() => {
    reset(defaultBookingDetailsData);
    setBookingComplete(false);
  }, [reset, setBookingComplete]);

  // Funciones helper para compatibilidad con componentes existentes
  const updateContactField = useCallback((field: keyof ContactInfoFormData, value: string) => {
    setValue(`contactInfo.${field}`, value, { shouldValidate: true, shouldDirty: true });
  }, [setValue]);

  const updateFlightField = useCallback((field: keyof FlightInfoFormData, value: string) => {
    setValue(`flightInfo.${field}`, value, { shouldValidate: true, shouldDirty: true });
  }, [setValue]);

  const updateAdditionalItemsField = useCallback((field: keyof AdditionalItemsFormData, value: string | { stopShop?: boolean; golfBags?: boolean; surfboards?: boolean; }) => {
    setValue(`additionalItems.${field}` as any, value, { shouldValidate: true, shouldDirty: true });
  }, [setValue]);

  const updateExtraService = useCallback((service: 'stopShop' | 'golfBags' | 'surfboards', value: boolean) => {
    setValue(`additionalItems.extraServices.${service}`, value, { shouldValidate: true, shouldDirty: true });
  }, [setValue]);

  return {
    // React Hook Form
    form,
    control,
    handleSubmit,
    errors,
    isValid,
    isDirty,
    setValue,
    getValues,
    watch,

    // Custom functions
    onSubmit,
    resetForm,

    // Compatibility functions
    updateContactField,
    updateFlightField,
    updateAdditionalItemsField,
    updateExtraService,

    // Data
    formData: watchedData,

    // State
    isBookingComplete: state.isBookingComplete
  };
};
