import { useQuery } from '@tanstack/react-query';
import type { Airlines } from '../types/airlines.types';
import { fetchAirlines } from '../services/airlines.service';

export function useAirlines() {
    const {
        data,
        isLoading: loading,
        error
    } = useQuery<Airlines[], Error>({
        queryKey: ['airlines'],
        queryFn: fetchAirlines
    });
    return {
        data: data ?? null,
        loading,
        error: error ? error.message : null
    };
}