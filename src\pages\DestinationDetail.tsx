
import { useParams } from 'react-router-dom';
import { useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SEO from '@/components/SEO';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Clock, DollarSign, MapPin, Star, Car, Smartphone, Award } from 'lucide-react';
import { useAnalytics } from '@/shared/hooks/analytics';
import { useHotelsByZone } from '@/features/hotels/hooks/useHotels';
import { BookingForm } from '@/components/booking-form';
import destinations from '@/data/destinations.json';

const DestinationDetail = () => {
  const { slug } = useParams<{ slug: string }>();
  const { trackPageView } = useAnalytics();
  
  const destination = slug ? destinations[slug as keyof typeof destinations] : null;

  // Cargar hoteles específicos de esta zona si el destino existe
  const {
    hotels: destinationHotels,
    isLoading: hotelsLoading,
    isError: hotelsError,
    totalHotels
  } = useHotelsByZone(
    { zone_id: destination?.zone_id || 0 },
    { enabled: !!destination?.zone_id }
  );

  useEffect(() => {
    // Scroll to top when component loads
    window.scrollTo(0, 0);
    
    if (destination) {
      trackPageView({ 
        pagePath: `/destinations/${slug}`, 
        pageTitle: `${destination.name} - Los Cabos Transportation` 
      });
    }
  }, [destination, slug, trackPageView]);

  if (!destination) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="pt-20 pb-20 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Destination Not Found</h1>
          <p className="text-gray-600">The destination you're looking for doesn't exist.</p>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <SEO 
        title={destination.seoTitle}
        description={destination.seoDescription}
        keywords={destination.keywords}
        canonicalUrl={`https://loscabostransportation.com/destinations/${slug}`}
      />
      <Header />
      
      {/* Hero Section */}
      <section className="pt-20 lg:pt-36 pb-12 bg-gradient-to-br from-cabo-blue to-cabo-orange">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white">
            <Badge className="bg-white/20 text-white mb-4">Premium Destination</Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Airport Transfer to {destination.name}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
              {destination.longDescription}
            </p>
            
            <div className="flex flex-wrap justify-center gap-6 mb-8">
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5" />
                <span>{destination.duration}</span>
              </div>
              <div className="flex items-center space-x-2">
                <DollarSign className="w-5 h-5" />
                <span>{destination.price}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Star className="w-5 h-5" />
                <span>Premium Service</span>
              </div>
            </div>
            
            <Button size="lg" className="bg-white text-cabo-blue hover:bg-gray-100 font-semibold px-8 py-3">
              Book Transfer Now
            </Button>
          </div>
        </div>
      </section>

      {/* Attractions Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
            Top Attractions in {destination.name}
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {destination.attractions.map((attraction, index) => (
              <Card key={index} className="hover-scale">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {attraction.name}
                      </h3>
                      <Badge variant="secondary" className="text-xs">
                        {attraction.type}
                      </Badge>
                    </div>
                    <MapPin className="w-5 h-5 text-cabo-blue" />
                  </div>
                  <p className="text-gray-600">{attraction.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Activities Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Activities & Experiences
            </h2>
            <p className="text-xl text-gray-600">
              Make the most of your visit to {destination.name}
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {destination.activities.map((activity, index) => (
              <div key={index} className="bg-white rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-shadow">
                <MapPin className="w-6 h-6 text-cabo-blue mx-auto mb-2" />
                <span className="text-sm font-medium text-gray-800">{activity}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Booking Form Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Book Your Transfer to {destination.name}
              </h2>
              <p className="text-xl text-gray-600 mb-2">
                Choose from {totalHotels} available hotels in this destination
              </p>
              {hotelsLoading && (
                <p className="text-sm text-gray-500">Loading hotels...</p>
              )}
              {hotelsError && (
                <p className="text-sm text-red-500">Error loading hotels. You can still enter your destination manually.</p>
              )}
            </div>
            
            <div className="bg-gray-50 rounded-2xl p-8">
              <BookingForm 
                variant="vertical"
                destinationZoneId={destination.zone_id}
                availableHotels={destinationHotels}
                autoNavigate={true}
                showContactInfo={false}
                showSpecialOffer={false}
                className="max-w-2xl mx-auto"
                searchMode="dropdown"
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-cabo-blue to-cabo-orange">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white rounded-2xl p-8 md:p-12 shadow-xl">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Visit {destination.name}?
            </h3>
            <p className="text-xl text-gray-600 mb-8">
              Book your airport transfer now and start your Los Cabos adventure
            </p>
            
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <Car className="w-8 h-8 text-cabo-blue mx-auto mb-2" />
                <h4 className="font-semibold text-gray-900">Professional Drivers</h4>
                <p className="text-gray-600 text-sm">Licensed and experienced</p>
              </div>
              <div className="text-center">
                <Smartphone className="w-8 h-8 text-cabo-blue mx-auto mb-2" />
                <h4 className="font-semibold text-gray-900">Real-Time Tracking</h4>
                <p className="text-gray-600 text-sm">Monitor your transfer</p>
              </div>
              <div className="text-center">
                <Award className="w-8 h-8 text-cabo-blue mx-auto mb-2" />
                <h4 className="font-semibold text-gray-900">Premium Vehicles</h4>
                <p className="text-gray-600 text-sm">Comfortable and clean</p>
              </div>
            </div>
            
            <Button size="lg" className="bg-gradient-cabo hover:opacity-90 text-white font-semibold px-8 py-3">
              Book Transfer to {destination.name}
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default DestinationDetail;
