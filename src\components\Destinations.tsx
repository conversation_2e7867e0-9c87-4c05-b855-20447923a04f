
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { MapPin, Navigation, Smartphone, MessageCircle } from 'lucide-react';

const Destinations = () => {
  const navigate = useNavigate();
  
  const destinations = [
    {
      id: 'cabo-san-lucas',
      name: 'Cabo San Lucas',
      description: 'The tourist heart of Los Cabos with vibrant nightlife and the famous El Arco.',
      duration: '45-60 min',
      price: 'From $45',
      highlights: ['El Arco', 'Marina', 'Lover\'s Beach']
    },
    {
      id: 'san-jose-del-cabo',
      name: 'San José del Cabo',
      description: 'Colonial town with art, culture and the beautiful tourist corridor.',
      duration: '20-30 min',
      price: 'From $35',
      highlights: ['Historic Center', 'Art District', 'San José Estuary']
    },
    {
      id: 'tourist-corridor',
      name: 'Tourist Corridor',
      description: 'Premium hotel zone with luxury resorts and world-renowned golf courses.',
      duration: '30-45 min',
      price: 'From $40',
      highlights: ['All-Inclusive Resorts', 'Golf Courses', 'Private Beaches']
    },
    // {
    //   id: 'east-cape',
    //   name: 'East Cape',
    //   description: 'Natural paradise for sport fishing lovers and tranquility seekers.',
    //   duration: '60-90 min',
    //   price: 'From $85',
    //   highlights: ['Sport Fishing', 'Virgin Beaches', 'Magical Towns']
    // },
    // {
    //   id: 'todos-santos',
    //   name: 'Todos Santos',
    //   description: 'Magical Town with colonial charm, art and the famous surf beach.',
    //   duration: '90-120 min',
    //   price: 'From $120',
    //   highlights: ['Magical Town', 'Art Galleries', 'Hotel California']
    // },
    // {
    //   id: 'la-paz',
    //   name: 'La Paz',
    //   description: 'State capital with beautiful beaches and unique ecotourism opportunities.',
    //   duration: '150-180 min',
    //   price: 'From $180',
    //   highlights: ['Malecón', 'Espíritu Santo Island', 'Historic Center']
    // }
  ];

  return (
    <section id="destinations-section" className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="bg-cabo-orange text-white mb-4">Popular Destinations</Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Discover Los Cabos and Surroundings
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We take you to the most incredible destinations in Baja California Sur. 
            From vibrant Cabo San Lucas to magical towns full of history.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {destinations.map((destination, index) => (
            <Card key={index} className="hover-scale bg-white shadow-lg border-0 overflow-hidden group">
              <div className="h-48 bg-gradient-sand flex items-center justify-center relative overflow-hidden">
                <MapPin className="w-16 h-16 text-white group-hover:scale-110 transition-transform duration-500" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              </div>
              
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-xl font-bold text-gray-900">
                    {destination.name}
                  </h3>
                  <div className="text-right">
                    <div className="text-sm font-semibold text-cabo-blue">
                      {destination.price}
                    </div>
                    <div className="text-xs text-gray-500">{destination.duration}</div>
                  </div>
                </div>

                <p className="text-gray-600 mb-4 leading-relaxed">
                  {destination.description}
                </p>

                <div className="space-y-3">
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Highlights:</h4>
                    <div className="flex flex-wrap gap-2">
                      {destination.highlights.map((highlight, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs bg-cabo-blue/10 text-cabo-blue">
                          {highlight}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <Button 
                    onClick={() => navigate(`/destinations/${destination.id}`)}
                    className="w-full bg-gradient-cabo hover:opacity-90 text-white"
                  >
                    Learn More & Book
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 md:p-12 shadow-lg">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Plan Your Perfect Itinerary
            </h3>
            <p className="text-xl text-gray-600 mb-6">
              Our local drivers know all the best kept secrets of Los Cabos
            </p>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div className="p-4">
                <Navigation className="w-8 h-8 text-cabo-blue mx-auto mb-2" />
                <h4 className="font-semibold text-gray-900">Optimized Routes</h4>
                <p className="text-gray-600 text-sm">The fastest and safest routes</p>
              </div>
              <div className="p-4">
                <Smartphone className="w-8 h-8 text-cabo-blue mx-auto mb-2" />
                <h4 className="font-semibold text-gray-900">Real-Time Tracking</h4>
                <p className="text-gray-600 text-sm">Monitor your transfer live</p>
              </div>
              <div className="p-4">
                <MessageCircle className="w-8 h-8 text-cabo-blue mx-auto mb-2" />
                <h4 className="font-semibold text-gray-900">Local Recommendations</h4>
                <p className="text-gray-600 text-sm">Tips for must-see places</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Destinations;
