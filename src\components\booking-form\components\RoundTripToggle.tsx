import { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';

interface RoundTripToggleProps {
  value: boolean;
  onChange: (value: boolean) => void;
  className?: string;
  label?: string;
}

export const RoundTripToggle = ({
  value,
  onChange,
  className,
  label = "Round Trip"
}: RoundTripToggleProps) => {
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const handleToggleChange = (newValue: boolean) => {
    // Si está cambiando de Round Trip (true) a One Way (false), mostrar modal
    if (value === true && newValue === false) {
      setShowConfirmModal(true);
    } else {
      // Si está cambiando de One Way a Round Trip, cambiar directamente
      onChange(newValue);
    }
  };

  const handleKeepRoundTrip = () => {
    setShowConfirmModal(false);
    // No cambiar el valor, mantener Round Trip
  };

  const handleChangeToOneWay = () => {
    setShowConfirmModal(false);
    onChange(false); // Cambiar a One Way
  };

  return (
    <>
      <div className={`flex items-center space-x-3 ${className ? className : ''}`}>
        <span className="text-sm font-medium text-gray-700">{label}</span>
        <Switch
          checked={value}
          onCheckedChange={handleToggleChange}
          className="data-[state=checked]:bg-cabo-blue"
        />
      </div>

      <Dialog open={showConfirmModal} onOpenChange={setShowConfirmModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="text-left">
            <DialogTitle className="flex items-center gap-3 text-xl font-semibold text-orange-600">
              <AlertTriangle className="h-6 w-6 text-orange-600" />
              Important Transportation Notice
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Secure Your Hassle-Free Transportation
            </h3>

            <DialogDescription className="text-gray-600 leading-relaxed">
              For your return to the airport, you'll need to take a taxi
              unless you've booked private round-trip transportation
              in advance. Don't leave your comfort to chance—book
              your round-trip transfer now and enjoy a worry-free
              journey while saving time and money.
            </DialogDescription>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 mt-6">
            <Button
              onClick={handleChangeToOneWay}
              className="bg-red-500 hover:bg-red-600 text-white font-medium px-6 py-2 rounded-md transition-colors"
            >
              Change to One Way
            </Button>
            <Button
              onClick={handleKeepRoundTrip}
              className="bg-cabo-blue hover:bg-cabo-blue/90 text-white font-medium px-6 py-2 rounded-md transition-colors"
            >
              Keep Round Trip
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
