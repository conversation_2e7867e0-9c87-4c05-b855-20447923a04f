import { Switch } from '@/components/ui/switch';

interface RoundTripToggleProps {
  value: boolean;
  onChange: (value: boolean) => void;
  className?: string;
  label?: string;
}

export const RoundTripToggle = ({
  value,
  onChange,
  className,
  label = "Round Trip"
}: RoundTripToggleProps) => {
  return (
    <div className={`flex items-center space-x-3 ${className ? className : ''}`}>
      <span className="text-sm font-medium text-gray-700">{label}</span>
      <Switch
        checked={value}
        onCheckedChange={onChange}
        className="data-[state=checked]:bg-cabo-blue"
      />
    </div>
  );
};
