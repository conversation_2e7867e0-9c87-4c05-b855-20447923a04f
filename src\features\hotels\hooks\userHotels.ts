import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useState, useEffect, useCallback } from 'react';
import { CACHE_CONFIG } from '../../../config/config';
import { hotelsService } from '../services/hotels.service';
import type {
  Hotel,
  HotelResponse,
  HotelByZoneResponse,
  HotelsQueryParams,
  HotelsByZoneParams,
  UseHotelsResult,
  UseHotelsByZoneResult,
} from '../types/hotels.types';

/**
 * Query keys for React Query caching
 */
export const hotelQueryKeys = {
  all: ['hotels'] as const,
  lists: () => [...hotelQueryKeys.all, 'list'] as const,
  list: (params: HotelsQueryParams) => [...hotelQueryKeys.lists(), params] as const,
  zones: () => [...hotelQueryKeys.all, 'zone'] as const,
  zone: (params: HotelsByZoneParams) => [...hotelQueryKeys.zones(), params] as const,
};

/**
 * Hook to fetch all hotels with optional filtering and pagination
 */
export function useHotels(
  params: HotelsQueryParams = {},
  options: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
    retry?: number;
    refetchOnWindowFocus?: boolean;
  } = {}
): UseHotelsResult {
  const {
    enabled = true,
    staleTime = CACHE_CONFIG.EXPIRATION.MEDIUM,
    cacheTime = CACHE_CONFIG.EXPIRATION.LONG,
    retry = 3,
    refetchOnWindowFocus = false,
  } = options;

  const queryResult = useQuery({
    queryKey: hotelQueryKeys.list(params),
    queryFn: () => hotelsService.getAllHotelsWithRetry(params),
    enabled,
    staleTime,
    gcTime: cacheTime, // React Query v5 uses gcTime instead of cacheTime
    retry,
    refetchOnWindowFocus,
    select: (data) => data, // Can be used for data transformation
  });

  const { data, isLoading, isError, error, refetch } = queryResult;

  // Calculate pagination info
  const currentPage = params.page || 1;
  const itemsPerPage = params.limit || 10;
  const totalItems = data?.total_hotels || 0;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  return {
    hotels: data?.hotels || [],
    totalHotels: totalItems,
    isLoading,
    isError,
    error: error as Error | null,
    refetch,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
    fetchNextPage: () => {
      if (currentPage < totalPages) {
        // This would typically be handled by parent component
        // by updating the params.page value
      }
    },
    fetchPreviousPage: () => {
      if (currentPage > 1) {
        // This would typically be handled by parent component
        // by updating the params.page value
      }
    },
  };
}

/**
 * Hook to fetch hotels by zone ID
 */
export function useHotelsByZone(
  params: HotelsByZoneParams,
  options: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
    retry?: number;
    refetchOnWindowFocus?: boolean;
  } = {}
): UseHotelsByZoneResult {
  const {
    enabled = true,
    staleTime = CACHE_CONFIG.EXPIRATION.MEDIUM,
    cacheTime = CACHE_CONFIG.EXPIRATION.LONG,
    retry = 3,
    refetchOnWindowFocus = false,
  } = options;

  const queryResult = useQuery({
    queryKey: hotelQueryKeys.zone(params),
    queryFn: () => hotelsService.getHotelsByZoneWithRetry(params),
    enabled: enabled && params.zone_id > 0,
    staleTime,
    gcTime: cacheTime,
    retry,
    refetchOnWindowFocus,
  });

  const { data, isLoading, isError, error, refetch } = queryResult;

  return {
    hotels: data?.hotels || [],
    totalHotels: data?.total_hotels || 0,
    zoneId: params.zone_id,
    zoneName: data?.zone_name || '',
    isLoading,
    isError,
    error: error as Error | null,
    refetch,
  };
}

/**
 * Hook to prefetch hotels data
 */
export function usePrefetchHotels() {
  const queryClient = useQueryClient();

  const prefetchAllHotels = async (params: HotelsQueryParams = {}) => {
    await queryClient.prefetchQuery({
      queryKey: hotelQueryKeys.list(params),
      queryFn: () => hotelsService.getAllHotelsWithRetry(params),
      staleTime: CACHE_CONFIG.EXPIRATION.MEDIUM,
    });
  };

  const prefetchHotelsByZone = async (params: HotelsByZoneParams) => {
    if (params.zone_id <= 0) return;
    
    await queryClient.prefetchQuery({
      queryKey: hotelQueryKeys.zone(params),
      queryFn: () => hotelsService.getHotelsByZoneWithRetry(params),
      staleTime: CACHE_CONFIG.EXPIRATION.MEDIUM,
    });
  };

  return {
    prefetchAllHotels,
    prefetchHotelsByZone,
  };
}

/**
 * Hook to manage hotels cache
 */
export function useHotelsCache() {
  const queryClient = useQueryClient();

  const invalidateAllHotels = () => {
    queryClient.invalidateQueries({
      queryKey: hotelQueryKeys.all,
    });
  };

  const invalidateHotelsList = (params?: HotelsQueryParams) => {
    if (params) {
      queryClient.invalidateQueries({
        queryKey: hotelQueryKeys.list(params),
      });
    } else {
      queryClient.invalidateQueries({
        queryKey: hotelQueryKeys.lists(),
      });
    }
  };

  const invalidateHotelsByZone = (params?: HotelsByZoneParams) => {
    if (params) {
      queryClient.invalidateQueries({
        queryKey: hotelQueryKeys.zone(params),
      });
    } else {
      queryClient.invalidateQueries({
        queryKey: hotelQueryKeys.zones(),
      });
    }
  };

  const removeHotelsCache = () => {
    queryClient.removeQueries({
      queryKey: hotelQueryKeys.all,
    });
  };

  const setHotelsData = (params: HotelsQueryParams, data: Hotel[]) => {
    queryClient.setQueryData(hotelQueryKeys.list(params), (oldData: HotelResponse | undefined) => ({
      success: true,
      hotels: data,
      total_hotels: data.length,
      message: oldData?.message,
    }));
  };

  const getHotelsData = (params: HotelsQueryParams) => {
    return queryClient.getQueryData(hotelQueryKeys.list(params));
  };

  return {
    invalidateAllHotels,
    invalidateHotelsList,
    invalidateHotelsByZone,
    removeHotelsCache,
    setHotelsData,
    getHotelsData,
  };
}

/**
 * Custom hook for hotels search with debouncing
 */
export function useHotelsSearch(
  initialParams: HotelsQueryParams = {},
  debounceMs: number = 300
) {
  const [searchParams, setSearchParams] = useState(initialParams);
  const [debouncedParams, setDebouncedParams] = useState(initialParams);

  // Debounce search parameters
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedParams(searchParams);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchParams, debounceMs]);

  const hotelsResult = useHotels(debouncedParams);

  const updateSearch = useCallback((newParams: Partial<HotelsQueryParams>) => {
    setSearchParams(prev => ({ ...prev, ...newParams }));
  }, []);

  const resetSearch = useCallback(() => {
    setSearchParams(initialParams);
  }, [initialParams]);

  return {
    ...hotelsResult,
    searchParams,
    updateSearch,
    resetSearch,
    isSearching: JSON.stringify(searchParams) !== JSON.stringify(debouncedParams),
  };
}

// Re-export types for convenience
export type {
  Hotel,
  HotelsQueryParams,
  HotelsByZoneParams,
  UseHotelsResult,
  UseHotelsByZoneResult,
} from '../types/hotels.types';