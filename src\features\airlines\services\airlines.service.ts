import { API_ENDPOINTS, ERROR_MESSAGES } from '../../../config/config';
import type { Airlines } from '../types/airlines.types';

export async function fetchAirlines(): Promise<Airlines[]> {
  const baseUrl = API_ENDPOINTS.AIRLINES.ALL;
  try {
    const response = await fetch(baseUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(ERROR_MESSAGES?.SERVER || 'Error fetching airlines');
    }

    const data = await response.json();
    return data as Airlines[];
  } catch (error: unknown) {
    // Optionally log error or handle it differently
    if (error instanceof Error) {
      throw new Error(error.message || ERROR_MESSAGES?.UNKNOWN || 'Unknown error');
    }
    throw new Error(ERROR_MESSAGES?.UNKNOWN || 'Unknown error');
  }
}
