// Unified BookingForm Component (Recommended)
export { default as BookingForm } from './BookingForm';
export type { BookingFormProps, BookingFormVariant } from './BookingForm';

// Legacy Booking Form Components (Backward Compatibility) - DEPRECATED
// These components have been moved to legacy/ folder and replaced by the unified BookingForm component
export { default as BookingFormEnhanced } from './legacy/BookingFormEnhanced';
export { default as BookingFormHorizontal } from './legacy/BookingFormHorizontal';
export { default as BookingFormVertical } from './legacy/BookingFormVertical';

// Hooks
export { useBookingForm } from './hooks/useBookingForm';

// Shared Components
export * from './components';

// Types from individual components (legacy)
export type {
  BookingFormEnhancedProps
} from './legacy/BookingFormEnhanced';

// Types from hooks
export type {
  UseBookingFormProps,
  BookingFormDataWithHotel,
  BookingFormData
} from './hooks/useBookingForm';

// Shared types
export type {
  LegacyBookingFormProps,
  ValidationErrors,
  DestinationInfo
} from './types';
