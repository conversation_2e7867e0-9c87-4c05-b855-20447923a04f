// Hotels Feature - Main Exports
// Re-export all types, services, hooks, and components

// Types
export type {
  Hotel,
  HotelResponse,
  HotelByZoneResponse,
  HotelsQueryParams,
  HotelsByZoneParams,
  HotelError,
  HotelApiError,
  UseHotelsResult,
  UseHotelsByZoneResult,
} from './types/hotels.types';

// Services
export { hotelsService } from './services/hotels.service';

// Hooks
export {
  useHotels,
  useHotelsByZone,
  useHotelsSearch,
  usePrefetchHotels,
  useHotelsCache,
  hotelQueryKeys,
} from './hooks/useHotels';

// Components
export { default as HotelsExample } from './components/HotelsExample';
export { default as HotelSearch } from './components/HotelSearch';
export { HotelSelectSearch } from './components/HotelSelectSearch';

// Utilities and Constants
export const HOTEL_CONSTANTS = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  SEARCH_MIN_LENGTH: 1,
  SEARCH_MAX_LENGTH: 100,
  DEBOUNCE_MS: 300,
} as const;

export const HOTEL_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  ALL: 'all',
} as const;

export const HOTEL_SORT_BY = {
  NAME: 'name',
  ZONE_NAME: 'zone_name',
  RATING: 'rating',
  CREATED_AT: 'created_at',
} as const;

export const HOTEL_SORT_ORDER = {
  ASC: 'asc',
  DESC: 'desc',
} as const;
