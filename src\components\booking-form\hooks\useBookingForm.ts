import { useState, useEffect, useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { useAnalytics } from '@/shared/hooks/analytics';
import { useAirports } from '@/features/airports';
import { useNavigate } from 'react-router-dom';
import useBookingPersistence, { getStoredBookingData } from '@/hooks/useBookingPersistence';
import type { Hotel } from '@/features/hotels';
import { useBooking } from '@/context/BookingContext';
import type { AirportInfo } from '@/context/BookingContext';

// Schema de validación con Zod
const createBookingFormSchema = (formType: string) => z.object({
  from: z.string().min(1, 'Please select pickup location'),
  to: z.string().min(1, 'Please enter destination'),
  date: z.date({ required_error: 'Please select date' }),
  time: formType === 'horizontal'
    ? z.string().optional()
    : z.string().min(1, 'Please select time'),
  returnDate: z.date().nullable().optional(),
  returnTime: z.string().optional(),
  adults: z.string().default('1'),
  kids: z.string().default('0'),
  passengers: z.string().optional(),
  roundTrip: z.boolean().default(true)
}).refine((data) => {
  // Si es round trip, la fecha de retorno es requerida
  if (data.roundTrip && !data.returnDate) {
    return false;
  }
  return true;
}, {
  message: "Return date is required for round trips",
  path: ["returnDate"]
});

// Creamos el schema para la variante horizontal
const horizontalSchema = createBookingFormSchema('horizontal');

// Tipo base para BookingFormData
export type BookingFormData = z.infer<typeof horizontalSchema>;

// Tipo extendido para onFormChange que incluye selectedHotel
export type BookingFormDataWithHotel = BookingFormData & { selectedHotel?: Hotel | null };

export interface UseBookingFormProps {
  onFormChange?: (data: BookingFormDataWithHotel) => void;
  initialData?: Partial<BookingFormData & { selectedHotel?: Hotel }>;
  formType?: 'horizontal' | 'vertical' | 'enhanced';
  autoNavigate?: boolean;
}

export interface UseBookingFormReturn {
  formState: {
    date: Date | null;
    from: string;
    to: string;
    selectedHotel: Hotel | null;
    time: string;
    returnDate: Date | null;
    returnTime: string;
    adults: string;
    kids: string;
    passengers?: string;
    roundTrip: boolean;
    errors: {
      from: boolean;
      to: boolean;
      date: boolean;
      time: boolean;
      returnDate: boolean;
      returnTime: boolean;
      adults: boolean;
      kids: boolean;
      passengers: boolean;
    };
  };
  airportData: {
    airportOptions: Array<{
      value?: string | number;
      label?: string;
      city?: string;
      code?: string;
    }>;
    loading: boolean;
    error: any;
  };
  handlers: {
    handleSubmit: (event: React.FormEvent) => Promise<void>;
    handleFromChange: (value: string) => void;
    handleHotelChange: (value: string, hotel?: Hotel) => void;
    updateField: (field: string, value: string | boolean | Date) => void;
  };
  helpers: {
    getDestinationZoneId: () => number | null;
  };
  form: any;
}

export const useBookingForm = ({
  onFormChange,
  initialData = {},
  formType = 'horizontal',
  autoNavigate = true
}: UseBookingFormProps = {}): UseBookingFormReturn => {
  
  // Hook para hoteles seleccionados
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(() => {
    if (initialData && 'selectedHotel' in initialData && initialData.selectedHotel) {
      return initialData.selectedHotel as Hotel;
    }
    return null;
  });

  // Hooks
  const { trackEvent } = useAnalytics();
  const navigate = useNavigate();
  const { airportOptions, loading: airportsLoading, error: airportsError } = useAirports();
  const { saveAndNavigate, saveBookingData } = useBookingPersistence();
  const { setFromAirport } = useBooking();

  // Configurar valores por defecto (moved outside to prevent re-computation)
  const [defaultValues] = useState(() => {
    // Prioridad 1: datos iniciales
    if (initialData && Object.keys(initialData).length > 0) {
      return {
        from: initialData.from || '',
        to: initialData.to || '',
        date: initialData.date || undefined,
        time: initialData.time || '',
        returnDate: initialData.returnDate || undefined,
        returnTime: initialData.returnTime || '',
        adults: initialData.adults || '1',
        kids: initialData.kids || '0',
        passengers: initialData.passengers || '',
        roundTrip: initialData.roundTrip !== undefined ? initialData.roundTrip : true
      };
    }

    // Prioridad 2: datos persistidos
    const persistedData = getStoredBookingData();
    if (persistedData) {
      return {
        from: persistedData.from || '',
        to: persistedData.to || '',
        date: persistedData.date ? new Date(persistedData.date) : undefined,
        time: persistedData.time || '',
        returnDate: persistedData.returnDate ? new Date(persistedData.returnDate) : undefined,
        returnTime: persistedData.returnTime || '',
        adults: persistedData.adults || '1',
        kids: persistedData.kids || '0',
        passengers: persistedData.passengers || '',
        roundTrip: persistedData.roundTrip !== undefined ? persistedData.roundTrip : true
      };
    }

    // Prioridad 3: valores por defecto
    return {
      from: '',
      to: '',
      date: undefined,
      time: '',
      returnDate: undefined,
      returnTime: '',
      adults: '1',
      kids: '0',
      passengers: '',
      roundTrip: true
    };
  });

  // Inicializar react-hook-form con el schema correspondiente
  const schema = createBookingFormSchema(formType);
  const form = useForm<BookingFormData>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: 'onChange'
  });

  const { watch, setValue, getValues, formState: { errors } } = form;

  // Observar cambios del formulario
  const watchedValues = watch();

  // FORZAR sincronización de selectedHotel cuando hay initialData
  useEffect(() => {
    if (initialData && 'selectedHotel' in initialData && initialData.selectedHotel && !selectedHotel) {
      setSelectedHotel(initialData.selectedHotel as Hotel);
      
      // También forzar el valor del input
      const hotelDisplay = initialData.selectedHotel.zone_name && initialData.selectedHotel.zone_name !== 'undefined'
        ? `${initialData.selectedHotel.name} - ${initialData.selectedHotel.zone_name}`
        : initialData.selectedHotel.name;
      
      setValue('to', hotelDisplay);
    }
  }, [initialData, selectedHotel, setValue]);

  // Notificar cambios del formulario con debounce para evitar pérdida de foco
  useEffect(() => {
    if (!onFormChange) return;

    const timeoutId = setTimeout(() => {
      const currentData = getValues();
      
      // Debug del estado actual
      console.log('📋 Form change notification:', {
        from: currentData.from,
        to: currentData.to,
        date: currentData.date,
        time: currentData.time,
        returnDate: currentData.returnDate,
        returnTime: currentData.returnTime,
        roundTrip: currentData.roundTrip,
        selectedHotel: selectedHotel?.name
      });
      
      onFormChange({
        ...currentData,
        selectedHotel,
        returnDate: currentData.roundTrip ? currentData.returnDate : null,
        returnTime: currentData.roundTrip ? currentData.returnTime : ''
      });
    }, 300); // Debounce de 300ms

    return () => clearTimeout(timeoutId);
  }, [
    watchedValues.from, 
    watchedValues.to, 
    watchedValues.date, 
    watchedValues.time,
    watchedValues.returnDate,
    watchedValues.returnTime,
    watchedValues.adults, 
    watchedValues.kids, 
    watchedValues.roundTrip, 
    selectedHotel, 
    onFormChange, 
    getValues
  ]);

  // Handler para selección de aeropuerto de origen
  const handleFromChange = useCallback((value: string) => {
    setValue('from', value);

    // Buscar información del aeropuerto seleccionado
    const selectedAirport = airportOptions.find(airport => airport.value === value);

    if (selectedAirport) {
      const airportInfo: AirportInfo = {
        id: selectedAirport.value?.toString() || '',
        name: selectedAirport.label || '',
        code: selectedAirport.code || '',
        city: selectedAirport.city || ''
      };

      // Actualizar el contexto con la información del aeropuerto
      setFromAirport(airportInfo);
    }
  }, [setValue, airportOptions, setFromAirport]);

  // Handler para selección de hotel
  const handleHotelChange = useCallback((value: string, hotel?: Hotel) => {
    setValue('to', value);
    setSelectedHotel(hotel || null);

    // Solo notificar cambios cuando se selecciona un hotel específico,
    // no durante la escritura para evitar pérdida de foco
    if (onFormChange && hotel !== undefined) {
      const currentData = getValues();
      onFormChange({
        ...currentData,
        to: value,
        selectedHotel: hotel || null
      });
    }
  }, [setValue, onFormChange, getValues]);

  // Helper para obtener zone_id del destino
  const getDestinationZoneId = useCallback((): number | null => {
    return selectedHotel?.zone_id || null;
  }, [selectedHotel]);





  // Handler para envío del formulario
  const handleSubmit = useCallback((event: React.FormEvent) => {
    const { setError } = form;
    return form.handleSubmit((data: BookingFormData) => {
      // Analytics específicos por tipo
      const labels = {
        horizontal: 'Hero Reservation Form - Horizontal',
        vertical: 'Hero Reservation Form - Vertical',
        enhanced: 'Enhanced Booking Form'
      };

      trackEvent({
        action: 'submit',
        category: 'Booking Form',
        label: labels[formType]
      });
      
      // Encontrar detalles del aeropuerto seleccionado
      const selectedAirport = airportOptions.find(airport => 
        airport.value?.toString() === data.from
      );
      
      // Obtener información completa del destino (simplificado)
      const destinationInfo = selectedHotel ? {
        type: 'hotel' as const,
        zone_id: selectedHotel.zone_id,
        zone_name: selectedHotel.zone_name,
        hotel_id: selectedHotel.id,
        hotel_name: selectedHotel.name,
        address: selectedHotel.address,
        destination_text: data.to
      } : {
        type: 'custom_address' as const,
        zone_id: null,
        zone_name: null,
        hotel_id: null,
        hotel_name: null,
        address: data.to,
        destination_text: data.to
      };
      const zoneId = getDestinationZoneId();
      
      // Preparar datos del formulario
      const formData = {
        from: data.from,
        fromDetails: selectedAirport ? {
          id: selectedAirport.value || '',
          name: selectedAirport.label || '',
          code: selectedAirport.code || '',
          city: selectedAirport.city || ''
        } : null,
        to: data.to,
        date: data.date ? format(data.date, 'yyyy-MM-dd') : '',
        time: formType === 'horizontal' ? '12:00' : data.time,
        returnDate: data.roundTrip && data.returnDate ? format(data.returnDate, 'yyyy-MM-dd') : '',
        returnTime: data.roundTrip ? (formType === 'horizontal' ? '12:00' : data.returnTime) : '',
        roundTrip: data.roundTrip,
        
        // Campos específicos según el tipo de formulario
        ...(formType === 'vertical' ? { passengers: data.passengers } : { adults: data.adults, kids: data.kids }),
        
        // Zone ID prominente para fácil acceso
        zone_id: zoneId,
        
        // Información detallada del destino
        destination: destinationInfo,
        
        // Mantener compatibilidad con estructura anterior
        selectedHotel: selectedHotel ? {
          id: selectedHotel.id,
          name: selectedHotel.name,
          zone_id: selectedHotel.zone_id,
          zone_name: selectedHotel.zone_name,
          address: selectedHotel.address,
        } : null,
      };
      
      // Validar return date cuando es round trip
      if (data.roundTrip) {
        if (!data.returnDate) {
          setError('returnDate', {
            type: 'manual',
            message: 'Return date is required for round trips'
          });
          return;
        }
        
        if (formType !== 'horizontal' && !data.returnTime) {
          setError('returnTime', {
            type: 'manual',
            message: 'Return time is required for round trips'
          });
          return;
        }
      }

      // Preparar los datos de la fecha de retorno
      if (data.roundTrip && !data.returnDate) {
        setError('returnDate', {
          type: 'manual',
          message: 'Return date is required for round trips'
        });
        return;
      }

      // Asegurarse de que la fecha de retorno se guarde correctamente
      const formDataToSave = {
        ...formData,
        returnDate: data.roundTrip && data.returnDate ? format(data.returnDate, 'yyyy-MM-dd') : null,
        returnTime: data.roundTrip ? (data.returnTime || (formType === 'horizontal' ? '12:00' : '')) : ''
      };

      // Solo navegar si autoNavigate es true
      if (autoNavigate) {
        saveAndNavigate(formDataToSave, '/booking-service');
      } else {
        // Si no navega, al menos guarda los datos
        saveBookingData(formDataToSave);
      }
      
      return formData;
    })(event);
  }, [form, formType, trackEvent, airportOptions, getDestinationZoneId, selectedHotel, autoNavigate, saveAndNavigate, saveBookingData]);

  // Funciones para actualizar campos individuales (compatibilidad con componentes existentes)
  const updateField = useCallback((field: keyof BookingFormData, value: string | Date | boolean | null) => {
    switch (field) {
      case 'from':
        setValue('from', value as string);
        break;
      case 'to':
        setValue('to', value as string);
        break;
      case 'date':
        setValue('date', value as Date);
        if (value instanceof Date) {
          saveBookingData({ date: format(value, 'yyyy-MM-dd') });
        } else if (value === null || value === undefined) {
          saveBookingData({ date: null });
        }
        break;
      case 'time':
        setValue('time', value as string);
        break;
      case 'returnDate':
        setValue('returnDate', value as Date | null);
        if (value instanceof Date) {
          saveBookingData({ returnDate: format(value, 'yyyy-MM-dd') });
        } else if (value === null || value === undefined) {
          saveBookingData({ returnDate: null });
        }
        break;
      case 'returnTime':
        setValue('returnTime', value as string);
        break;
      case 'adults':
        setValue('adults', value as string);
        break;
      case 'kids':
        setValue('kids', value as string);
        break;
      case 'passengers':
        setValue('passengers', value as string);
        break;
      case 'roundTrip':
        setValue('roundTrip', value as boolean);
        if (typeof value === 'boolean') {
          // Si se cambia a one-way, limpiar la fecha de retorno
          if (!value) {
            setValue('returnDate', null);
            setValue('returnTime', '');
            saveBookingData({ 
              roundTrip: value,
              returnDate: null,
              returnTime: ''
            });
          } else {
            saveBookingData({ roundTrip: value });
          }
        }
        break;
    }
  }, [setValue, saveBookingData]);

  return {
    // Estado del formulario usando react-hook-form
    formState: {
      date: watchedValues.date,
      from: watchedValues.from,
      to: watchedValues.to,
      selectedHotel,
      time: watchedValues.time,
      returnDate: watchedValues.returnDate,
      returnTime: watchedValues.returnTime,
      adults: watchedValues.adults,
      kids: watchedValues.kids,
      passengers: watchedValues.passengers,
      roundTrip: watchedValues.roundTrip,
      errors: {
        from: !!errors.from,
        to: !!errors.to,
        date: !!errors.date,
        time: !!errors.time,
        returnDate: !!errors.returnDate,
        returnTime: !!errors.returnTime,
        adults: !!errors.adults,
        kids: !!errors.kids,
        passengers: !!errors.passengers,
      }
    },
    
    // Datos de aeropuertos
    airportData: {
      airportOptions,
      loading: airportsLoading,
      error: airportsError
    },
    
    // Funciones
    handlers: {
      handleSubmit,
      handleFromChange,
      handleHotelChange,
      updateField
    },
    
    // Helpers
    helpers: {
      getDestinationZoneId
    },

    // Exponer react-hook-form para uso avanzado
    form
  };
};
