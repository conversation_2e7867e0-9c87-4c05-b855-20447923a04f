<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Hotels API</title>
</head>
<body>
    <h1>Debug Hotels API</h1>
    <div>
        <h2>Test 1: Todos los hoteles</h2>
        <button onclick="testAllHotels()">Obtener todos los hoteles</button>
        <div id="allHotelsResult"></div>
    </div>
    
    <div>
        <h2>Test 2: Buscar "Corazon"</h2>
        <button onclick="testSearchCorazon()">Buscar Corazon</button>
        <div id="searchResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost/bajatravel/index.php/api/v1';
        
        async function testAllHotels() {
            try {
                console.log('Fetching all hotels...');
                const response = await fetch(`${API_BASE}/hotels`);
                const data = await response.json();
                
                console.log('All hotels response:', data);
                
                // Buscar hotel con "Corazon" en el nombre
                const corazonHotels = data.hotels?.filter(hotel => 
                    hotel.name.toLowerCase().includes('corazon')
                ) || [];
                
                const resultDiv = document.getElementById('allHotelsResult');
                resultDiv.innerHTML = `
                    <h3>Resultado:</h3>
                    <p>Total hoteles: ${data.hotels?.length || 0}</p>
                    <p>Hoteles con "Corazon": ${corazonHotels.length}</p>
                    ${corazonHotels.length > 0 ? `
                        <h4>Hoteles encontrados:</h4>
                        <ul>
                            ${corazonHotels.map(hotel => `
                                <li>ID: ${hotel.id}, Nombre: ${hotel.name}, Zona: ${hotel.zone_name}</li>
                            `).join('')}
                        </ul>
                    ` : '<p>No se encontraron hoteles con "Corazon" en el nombre</p>'}
                `;
                
            } catch (error) {
                console.error('Error fetching all hotels:', error);
                document.getElementById('allHotelsResult').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        async function testSearchCorazon() {
            try {
                console.log('Searching for Corazon...');
                const response = await fetch(`${API_BASE}/hotels?search=Corazon`);
                const data = await response.json();
                
                console.log('Search response:', data);
                
                const resultDiv = document.getElementById('searchResult');
                resultDiv.innerHTML = `
                    <h3>Resultado de búsqueda:</h3>
                    <p>Success: ${data.success}</p>
                    <p>Total encontrados: ${data.total_hotels || 0}</p>
                    <p>Hoteles en respuesta: ${data.hotels?.length || 0}</p>
                    ${data.hotels?.length > 0 ? `
                        <h4>Hoteles encontrados:</h4>
                        <ul>
                            ${data.hotels.map(hotel => `
                                <li>ID: ${hotel.id}, Nombre: ${hotel.name}, Zona: ${hotel.zone_name}</li>
                            `).join('')}
                        </ul>
                    ` : '<p>No se encontraron hoteles</p>'}
                `;
                
            } catch (error) {
                console.error('Error searching hotels:', error);
                document.getElementById('searchResult').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
