
import { Helmet } from 'react-helmet-async';

const StructuredData = () => {
  const transportationSchema = {
    "@context": "https://schema.org",
    "@type": "TransportationCompany",
    "name": "Los Cabos Airport Transportation",
    "description": "Premium transportation service from Los Cabos SJD Airport to hotels and destinations in Los Cabos",
    "url": "https://loscabostransportation.com",
    "telephone": "+52-624-XXX-XXXX",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Los Cabos",
      "addressRegion": "Baja California Sur",
      "addressCountry": "MX"
    },
    "serviceArea": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": 23.1518,
        "longitude": -109.7207
      },
      "geoRadius": "50000"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Transportation Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Airport - Hotel Transfer",
            "description": "Transfer service from Los Cabos SJD Airport to hotels"
          },
          "price": "45",
          "priceCurrency": "USD"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Premium SUV Service",
            "description": "Luxury SUV transfer for up to 6 passengers"
          },
          "price": "65",
          "priceCurrency": "USD"
        }
      ]
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "1000"
    }
  };

  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Los Cabos Airport Transportation",
    "image": "https://lovable.dev/opengraph-image-p98pqg.png",
    "description": "Leading company in ground transportation services in Los Cabos",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Los Cabos",
      "addressRegion": "Baja California Sur",
      "addressCountry": "MX"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 23.1518,
      "longitude": -109.7207
    },
    "openingHoursSpecification": {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": [
        "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"
      ],
      "opens": "00:00",
      "closes": "23:59"
    }
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(transportationSchema)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(localBusinessSchema)}
      </script>
    </Helmet>
  );
};

export default StructuredData;
