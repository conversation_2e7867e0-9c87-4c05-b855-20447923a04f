
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Shield, MapPin, Coffee, Car, Wifi } from 'lucide-react';

const Services = () => {
  const services = [
    {
      icon: Users,
      title: 'Bilingual Drivers',
      description: 'Trained staff who speak Spanish and English for a better travel experience.'
    },
    {
      icon: Shield,
      title: 'Insurance Included',
      description: 'Full coverage and vehicles with all permits required by authorities.'
    },
    {
      icon: MapPin,
      title: 'Tours & Excursions',
      description: 'Discover the best attractions in Los Cabos with our personalized tours.'
    },
    {
      icon: Coffee,
      title: 'Complimentary Drinks',
      description: 'Fresh water and welcome beverages during your transfer to the hotel.'
    },
    {
      icon: Car,
      title: 'Luxury Vehicles',
      description: 'Modern fleet with air conditioning, WiFi and comfortable seats for your comfort.'
    },
    {
      icon: Wifi,
      title: 'WiFi Connection',
      description: 'Free internet in all our vehicles to keep you connected.'
    }
  ];

  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="bg-cabo-blue text-white mb-4">Our Services</Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why Choose Our Transportation Service?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We offer more than just a simple transfer. We provide a complete experience 
            from the moment you arrive at the airport until you reach your destination.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <Card key={index} className="hover-scale bg-white shadow-lg border-0 overflow-hidden group">
                <CardContent className="p-6 text-center">
                  <div className="mb-4 group-hover:scale-110 transition-transform duration-300 flex justify-center">
                    <IconComponent className="w-12 h-12 text-cabo-blue" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {service.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="text-center mt-16">
          <div className="bg-gradient-cabo text-white rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Book in Advance and Save
            </h3>
            <p className="text-xl mb-6 opacity-90">
              Get up to 15% discount by booking your transfer 48 hours in advance
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="bg-white/20 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold">24/7</div>
                <div className="text-sm">Availability</div>
              </div>
              <div className="bg-white/20 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold">+1000</div>
                <div className="text-sm">Satisfied Customers</div>
              </div>
              <div className="bg-white/20 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold">5★</div>
                <div className="text-sm">Average Rating</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
