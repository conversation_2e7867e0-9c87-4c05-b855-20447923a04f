# Legacy Booking Form Components

⚠️ **DEPRECATED COMPONENTS** ⚠️

These components have been **deprecated** and replaced by the unified `BookingForm` component.

## 🚫 Status: OBSOLETE

- **BookingFormVertical.tsx** - Replaced by `<BookingForm variant="vertical" />`
- **BookingFormEnhanced.tsx** - Replaced by `<BookingForm variant="enhanced" />`
- **BookingFormHorizontal.tsx** - Replaced by `<BookingForm variant="horizontal" />`

## 🔄 Migration Guide

### Before (Legacy)
```tsx
import { 
  BookingFormVertical,
  BookingFormEnhanced,
  BookingFormHorizontal 
} from '@/components/booking-form';

// Old usage
<BookingFormVertical />
<BookingFormEnhanced onFormChange={handleChange} />
<BookingFormHorizontal />
```

### After (Current)
```tsx
import { BookingForm } from '@/components/booking-form';

// New unified usage
<BookingForm variant="vertical" autoNavigate={true} />
<BookingForm variant="enhanced" onFormChange={handleChange} autoNavigate={false} />
<BookingForm variant="horizontal" autoNavigate={true} />
```

## 📅 Timeline

- **Created**: Initial implementation
- **Deprecated**: 2025-01-05
- **Status**: Moved to legacy folder, maintained for backward compatibility only
- **Recommendation**: Use the unified `BookingForm` component instead

## ⚡ Why Deprecated?

1. **Code Duplication**: Each component had similar logic duplicated
2. **Maintenance Overhead**: Three separate components to maintain
3. **Inconsistent APIs**: Different prop interfaces across components
4. **Bundle Size**: Unnecessary code splitting

## 🎯 Unified Solution

The new `BookingForm` component provides:
- ✅ Single component with multiple variants
- ✅ Consistent API across all layouts
- ✅ Shared logic and reduced duplication
- ✅ Better maintainability
- ✅ Smaller bundle size

## 🗑️ Future Plans

These files may be removed in a future major version. Please migrate to the unified `BookingForm` component.
