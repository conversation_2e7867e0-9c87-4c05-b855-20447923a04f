
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useTheme } from '@/shared/hooks/theme';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { logos, company } = useTheme();

  return (
    <footer id="contact" className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center mb-6">
              <img 
                src={logos.footer || logos.main}
                alt={company.name}
                className="h-12 w-auto"
              />
            </div>
            
            <p className="text-gray-300 mb-6 leading-relaxed max-w-md">
              Premium ground transportation service from Los Cabos SJD Airport. 
              Over 10 years providing safe and comfortable transfers to tourists from around the world.
            </p>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <span className="text-cabo-blue">📞</span>
                <span>{company.phone}</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-cabo-blue">📧</span>
                <span>{company.email}</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-cabo-blue">📍</span>
                <span>{company.address}</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-cabo-blue">🕒</span>
                <span>Available 24/7, 365 days a year</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><a href="#home" className="text-gray-300 hover:text-cabo-blue transition-colors">Home</a></li>
              <li><a href="#services" className="text-gray-300 hover:text-cabo-blue transition-colors">Services</a></li>
              <li><a href="#fleet" className="text-gray-300 hover:text-cabo-blue transition-colors">Our Fleet</a></li>
              <li><a href="#destinations-section" className="text-gray-300 hover:text-cabo-blue transition-colors">Destinations</a></li>
              <li><a href="#" className="text-gray-300 hover:text-cabo-blue transition-colors">Rates</a></li>
              <li><a href="#" className="text-gray-300 hover:text-cabo-blue transition-colors">Policies</a></li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Newsletter</h3>
            <p className="text-gray-300 mb-4 text-sm">
              Receive special offers and travel tips for Los Cabos
            </p>
            <div className="space-y-3">
              <Input 
                placeholder="Your email" 
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
              />
              <Button className="w-full bg-gradient-cabo hover:opacity-90 text-white font-semibold">
                Subscribe
              </Button>
            </div>
            
            {/* Social Media */}
            <div className="mt-6">
              <h4 className="font-semibold mb-3">Follow Us</h4>
              <div className="flex space-x-3">
                <div className="w-8 h-8 bg-cabo-blue rounded-full flex items-center justify-center hover:bg-cabo-turquoise transition-colors cursor-pointer">
                  <span className="text-white text-sm">f</span>
                </div>
                <div className="w-8 h-8 bg-cabo-blue rounded-full flex items-center justify-center hover:bg-cabo-turquoise transition-colors cursor-pointer">
                  <span className="text-white text-sm">ig</span>
                </div>
                <div className="w-8 h-8 bg-cabo-blue rounded-full flex items-center justify-center hover:bg-cabo-turquoise transition-colors cursor-pointer">
                  <span className="text-white text-sm">wa</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-300 text-sm">
              © {currentYear} {company.name}. All rights reserved.
            </div>
            
            <div className="flex space-x-6 text-sm">
              <a href="#" className="text-gray-300 hover:text-cabo-blue transition-colors">
                Terms & Conditions
              </a>
              <a href="#" className="text-gray-300 hover:text-cabo-blue transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-300 hover:text-cabo-blue transition-colors">
                Legal Notice
              </a>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-800 text-center text-xs text-gray-400">
            <p>
              Company authorized by the Secretary of Tourism of Baja California Sur | 
              Federal Tourism License #********* | 
              Valid Civil Liability Insurance
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
