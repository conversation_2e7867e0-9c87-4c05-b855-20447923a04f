import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface PassengerSelectProps {
  value: string;
  onChange: (value: string) => void;
  error?: boolean;
  className?: string;
  type?: 'adults' | 'kids' | 'total';
  errorText?: string;
}

export const PassengerSelect = ({
  value,
  onChange,
  error,
  className,
  type,
  errorText
}: PassengerSelectProps) => {
  const getOptions = () => {
    switch (type) {
      case 'adults':
        return Array.from({ length: 16 }, (_, i) => {
          const num = i + 1;
          return { value: num.toString(), label: `${num} Adult${num > 1 ? 's' : ''}` };
        });
      case 'kids':
        return Array.from({ length: 6 }, (_, i) => ({
          value: i.toString(),
          label: `${i} Kid${i > 1 ? 's' : ''}`
        }));
      case 'total':
        return Array.from({ length: 14 }, (_, i) => {
          const num = i + 1;
          return { value: num.toString(), label: `${num} Passenger${num > 1 ? 's' : ''}` };
        });
      default:
        return [];
    }
  };

  const getLabel = () => {
    switch (type) {
      case 'adults': return 'Adults';
      case 'kids': return 'Kids';
      case 'total': return 'Passengers';
    }
  };

  const getPlaceholder = () => {
    switch (type) {
      case 'adults': return 'Adults';
      case 'kids': return 'Kids';
      case 'total': return 'Passengers';
    }
  };

  return (
    <div>
      {/* <label className="block text-sm font-medium text-gray-700 mb-2">
        {getLabel()}
      </label> */}
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className={cn("h-12", error && "border-red-500", className)}>
          <SelectValue placeholder={getPlaceholder()} />
        </SelectTrigger>
        <SelectContent className="bg-white z-50">
          {getOptions().map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && <p className="text-red-500 text-xs mt-1">{errorText || "Required"}</p>}
    </div>
  );
};
