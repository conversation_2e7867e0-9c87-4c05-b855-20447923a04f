import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { CalendarIcon, MapPinIcon, ClockIcon, UsersIcon, PhoneIcon, MailIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBookingForm } from '../hooks/useBookingForm';
import { useCallback } from 'react';
import type { Hotel } from '@/features/hotels';
import {
  OriginSelect,
  DestinationSearch,
  DatePicker,
  TimeSelect,
  PassengerSelect,
  RoundTripToggle
} from '../components';

export interface BookingFormData {
  from: string;
  to: string;
  date: Date | null;
  time: string;
  adults: string;
  kids: string;
  roundTrip: boolean;
  selectedHotel?: Hotel | null;
}

export interface BookingFormEnhancedProps {
  onFormChange?: (data: BookingFormData) => void;
  initialData?: {
    from?: string;
    to?: string;
    date?: Date | null;
    time?: string;
    adults?: string;
    kids?: string;
    roundTrip?: boolean;
    selectedHotel?: Hotel | null;
  };
}

const BookingFormEnhanced = ({ onFormChange, initialData }: BookingFormEnhancedProps) => {
  const { formState, airportData, handlers } = useBookingForm({ 
    onFormChange, 
    initialData,
    formType: 'enhanced' 
  });

  // Memoizamos la función de notificación de cambios para prevenir renders innecesarios
  const notifyChanges = useCallback(() => {
    if (onFormChange) {
      onFormChange({
        from: formState.from,
        to: formState.to,
        date: formState.date,
        time: formState.time,
        adults: formState.adults,
        kids: formState.kids,
        roundTrip: formState.roundTrip,
        selectedHotel: formState.selectedHotel
      });
    }
  }, [
    formState.from,
    formState.to,
    formState.date,
    formState.time,
    formState.adults,
    formState.kids,
    formState.roundTrip,
    formState.selectedHotel,
    onFormChange
  ]);

  // Manejador optimizado para cambios de formulario que excluye selectedHotel
  const handleFieldChange = useCallback((
    field: Exclude<keyof BookingFormData, 'selectedHotel'>,
    value: string | Date | null | boolean
  ) => {
    handlers.updateField(field, value);
    // Notificamos cambios después de una actualización
    setTimeout(notifyChanges, 0);
  }, [handlers, notifyChanges]);

  const handleFormSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    notifyChanges();
  };

  return (
    <Card className="bg-gradient-to-br from-primary to-accent text-white shadow-2xl">
      <CardContent className="p-4 md:p-8">
        <form className="space-y-6" onSubmit={handleFormSubmit}>
          {/* Header */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold mb-2 flex items-center justify-center gap-2">
              <CalendarIcon className="w-6 h-6" />
              Book Your Transfer
            </h2>
            <p className="text-white/90 text-sm">Choose your perfect transportation solution</p>
          </div>

          {/* Round Trip Toggle */}
          <div className="p-4 bg-white/10 rounded-lg backdrop-blur-sm">
            <RoundTripToggle
              value={formState.roundTrip}
              onChange={(checked) => handleFieldChange('roundTrip', checked)}
              label="Round Trip"
              className="[&>span]:text-white [&>span]:font-medium"
            />
          </div>

          {/* Form Fields */}
          <div className="form-fields">
            {/* Pickup Location */}
            <div className="space-y-1">
              <Label className="text-white text-sm font-medium">
                <MapPinIcon className="w-4 h-4 inline mr-2" />
                Pickup Location
              </Label>
              <OriginSelect
                value={formState.from}
                onChange={(value) => handleFieldChange('from', value)}
                error={formState.errors.from}
                airportOptions={airportData.airportOptions}
                loading={airportData.loading}
                className="bg-white/10 border-white/20 text-white placeholder:text-white/70"
                errorText="Please select pickup location"
              />
            </div>

            {/* Dropoff Location */}
            <div className="space-y-2">
              <Label className="text-white text-sm font-medium">
                <MapPinIcon className="w-4 h-4 inline mr-2" />
                Destination
              </Label>
              <DestinationSearch
                value={formState.to}
                onChange={(value, hotel) => {
                  handleFieldChange('to', value);
                  handlers.handleHotelChange(value, hotel);
                  setTimeout(notifyChanges, 0);
                }}
                error={formState.errors.to}
                className="bg-white/10 border-white/20 text-white placeholder:text-white/70"
                errorText="Please enter destination"
                selectedHotel={formState.selectedHotel}
              />
            </div>

            {/* Date and Time */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">
                  <CalendarIcon className="w-4 h-4 inline mr-2" />
                  Date
                </Label>
                <DatePicker
                  value={formState.date}
                  onChange={(date) => handleFieldChange('date', date)}
                  error={formState.errors.date}
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                  errorText="Please select date"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">
                  <ClockIcon className="w-4 h-4 inline mr-2" />
                  Time
                </Label>
                <TimeSelect
                  value={formState.time}
                  onChange={(value) => handleFieldChange('time', value)}
                  error={formState.errors.time}
                  className="bg-white/10 border-white/20 text-white"
                  errorText="Please select time"
                />
              </div>
            </div>

            {/* Passengers */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">
                  <UsersIcon className="w-4 h-4 inline mr-2" />
                  Adults
                </Label>
                <PassengerSelect
                  value={formState.adults}
                  onChange={(value) => handleFieldChange('adults', value)}
                  type="adults"
                  className="bg-white/10 border-white/20 text-white"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">
                  <UsersIcon className="w-4 h-4 inline mr-2" />
                  Kids
                </Label>
                <PassengerSelect
                  value={formState.kids}
                  onChange={(value) => handleFieldChange('kids', value)}
                  type="kids"
                  className="bg-white/10 border-white/20 text-white"
                />
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="mt-8 pt-6 border-t border-white/20">
            <h3 className="font-bold mb-4 text-lg">Contact Information</h3>
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-3 p-3 bg-white/10 rounded-lg">
                <PhoneIcon className="w-5 h-5 text-white/80" />
                <div>
                  <div className="font-medium">Phone</div>
                  <div className="text-white/90">+52 **************</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-white/10 rounded-lg">
                <MailIcon className="w-5 h-5 text-white/80" />
                <div>
                  <div className="font-medium">Email</div>
                  <div className="text-white/90 text-xs"><EMAIL></div>
                </div>
              </div>
            </div>
          </div>

          {/* Special Offer Banner */}
          <div className="mt-6 p-6 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl border border-yellow-400/30 text-center">
            <div className="text-2xl font-bold text-yellow-300 mb-2">💰 Save Money</div>
            <div className="text-lg font-semibold">Book Your Round Trip</div>
            <div className="text-sm mt-1 text-white/90">Get the best deals on return transfers</div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default BookingFormEnhanced;