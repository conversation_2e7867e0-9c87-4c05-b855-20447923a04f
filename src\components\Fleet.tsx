
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Car, Users, Truck, Crown } from 'lucide-react';
import { useState } from 'react';
import SpecialQuoteModal from '@/components/SpecialQuoteModal';

const Fleet = () => {
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);

  const vehicles = [
    {
      name: 'SUV Transportation',
      capacity: '1-5 Passengers',
      price: '$80',
      features: ['Air Conditioning', 'Free WiFi', 'Bilingual Driver'],
      icon: Car,
      popular: false
    },
    {
      name: 'LUXURY Transportation',
      capacity: '1-5 Passengers',
      price: '$100',
      features: ['Extra Space', 'Leather Seats', 'Premium Audio System'],
      icon: Truck,
      popular: true
    },
    {
      name: 'Van Transportation',
      capacity: '7-14 Passengers',
      price: '$100',
      features: ['Ideal for Groups', 'Luggage Space', 'Maximum Comfort'],
      icon: Users,
      popular: false
    }
  ];

  return (
    <section id="fleet" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="bg-cabo-turquoise text-white mb-4">Our Fleet</Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Modern and Comfortable Vehicles
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We have a modern fleet of vehicles equipped with the best amenities 
            to make your trip an unforgettable experience.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {vehicles.map((vehicle, index) => {
            const IconComponent = vehicle.icon;
            return (
              <Card key={index} className={`hover-scale bg-white shadow-lg border-0 overflow-hidden relative ${vehicle.popular ? 'ring-2 ring-cabo-orange animate-enter' : ''}`}>
                {vehicle.popular && (
                  <Badge className="absolute top-4 right-4 bg-cabo-orange text-white z-10 pulse">
                    Luxury Pick
                  </Badge>
                )}
                
                <div className="h-48 bg-gradient-to-br from-cabo-blue to-cabo-turquoise flex items-center justify-center relative">
                  {vehicle.popular && (
                    <span className="absolute top-3 left-3 text-white/90">
                      <Crown className="w-6 h-6" aria-hidden="true" />
                      <span className="sr-only">Luxury transportation</span>
                    </span>
                  )}
                  <IconComponent className={`w-20 h-20 text-white ${vehicle.popular ? 'drop-shadow-xl' : ''}`} />
                </div>
                
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-1">
                        {vehicle.name}
                      </h3>
                      <p className="text-gray-600">{vehicle.capacity}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-cabo-blue">
                        {vehicle.price}
                      </div>
                      <div className="text-sm text-gray-500">USD</div>
                    </div>
                  </div>

                  <ul className="space-y-2 mb-6">
                    {vehicle.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-gray-600">
                        <div className="w-2 h-2 bg-cabo-blue rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <Button 
                    className={`w-full font-semibold ${
                      vehicle.popular 
                        ? 'bg-gradient-to-r from-cabo-orange to-cabo-orange/80 hover:opacity-90 text-white' 
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                    }`}
                  >
                    Book Now
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="text-center mt-16">
          <div className="bg-gray-50 rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Need a Special Vehicle?
            </h3>
            <p className="text-xl text-gray-600 mb-6">
              Contact us for personalized services, VIP vehicles or transfers for special events
            </p>
            <Button 
              size="lg" 
              className="bg-gradient-cabo hover:opacity-90 text-white font-semibold px-8 py-3"
              onClick={() => setIsQuoteModalOpen(true)}
            >
              Request Special Quote
            </Button>
          </div>
        </div>
      </div>

      <SpecialQuoteModal 
        open={isQuoteModalOpen} 
        onOpenChange={setIsQuoteModalOpen} 
      />
    </section>
  );
};

export default Fleet;
