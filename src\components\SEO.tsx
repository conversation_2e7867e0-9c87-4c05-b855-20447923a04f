
import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  ogType?: string;
  canonicalUrl?: string;
}

const SEO = ({
  title = "Los Cabos Airport Transportation - Safe SJD Airport Transfers",
  description = "Premium ground transportation service from Los Cabos SJD Airport to hotels. Safe, comfortable and reliable transfers in Los Cabos, Baja California Sur.",
  keywords = "los cabos airport transportation, SJD transfers, airport shuttle los cabos, cabo san lucas transportation, private transfer los cabos",
  ogImage = "/lovable-uploads/19581b1f-fd65-4e4a-805a-2bfa9a9f4623.png",
  ogType = "website",
  canonicalUrl = "https://loscabostransportation.com/"
}: SEOProps) => {
  return (
    <Helmet>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="Los Cabos Airport Transportation" />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:url" content={canonicalUrl} />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Additional SEO meta tags */}
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Language" content="en" />
    </Helmet>
  );
};

export default SEO;
