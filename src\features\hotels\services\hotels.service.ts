import { API_ENDPOINTS, API_CONFIG, ERROR_MESSAGES } from '../../../config/config';
import type {
  Hotel,
  HotelResponse,
  HotelByZoneResponse,
  HotelsQueryParams,
  HotelsByZoneParams,
  HotelApiError,
  ApiError,
  ApiHotel,
  ApiHotelResponse,
} from '../types/hotels.types';


/**
 * Hotels Service
 * Handles all API interactions for hotel data with validation and error handling
 */
class HotelsService {
  private readonly baseHeaders = {
    'Accept': 'application/json',
  } as const;

  /**
   * Create fetch options with timeout and headers
   */
  private createFetchOptions(options: RequestInit = {}): RequestInit {
    return {
      ...options,
      headers: {
        ...this.baseHeaders,
        ...options.headers,
      },
      signal: options.signal || AbortSignal.timeout(API_CONFIG.TIMEOUT),
    };
  }

  /**
   * Handle API response and errors
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData: HotelApiError = await response.json().catch(() => ({
        success: false,
        error: {
          message: ERROR_MESSAGES.UNKNOWN,
          code: 'UNKNOWN_ERROR',
        },
        status_code: response.status,
      }));

      const error: ApiError = new Error(errorData.error.message || ERROR_MESSAGES.UNKNOWN);
      error.status = response.status;
      error.code = errorData.error.code;
      throw error;
    }

    const data = await response.json();
    
    // Validate response structure
    if (typeof data !== 'object' || data === null) {
      throw new Error('Invalid response format');
    }

    return data;
  }

  /**
   * Transform API hotel data to frontend Hotel interface
   */
  private transformHotelData(apiHotel: ApiHotel): Hotel {
    return {
      id: parseInt(apiHotel.id_hotel) || 0,
      name: apiHotel.nombre || '',
      zone_id: parseInt(apiHotel.zones_idzones.toString()) || 0,
      zone_name: apiHotel.zone_name || undefined,
      address: apiHotel.address || undefined,
      description: apiHotel.description || undefined,
      status: 'active', // Assuming API only returns active hotels
      created_at: apiHotel.created_at || undefined,
      updated_at: apiHotel.updated_at || undefined,
    };
  }

  /**
   * Validate hotel object structure
   */
  private validateHotel(hotel: unknown): hotel is Hotel {
    return (
      hotel !== null &&
      typeof hotel === 'object' &&
      'id' in hotel &&
      'name' in hotel &&
      'zone_id' in hotel &&
      typeof (hotel as Record<string, unknown>).id === 'number' &&
      typeof (hotel as Record<string, unknown>).name === 'string' &&
      typeof (hotel as Record<string, unknown>).zone_id === 'number'
    );
  }

  /**
   * Validate and sanitize query parameters
   */
  private validateAndSanitizeParams(params: HotelsQueryParams): URLSearchParams {
    const searchParams = new URLSearchParams();

    // Validate and add page parameter
    if (params.page !== undefined) {
      const page = Math.max(1, Math.floor(params.page));
      searchParams.append('page', page.toString());
    }

    // Validate and add limit parameter
    if (params.limit !== undefined) {
      const limit = Math.max(1, Math.min(100, Math.floor(params.limit)));
      searchParams.append('limit', limit.toString());
    }

    // Validate and add zone_id parameter
    if (params.zone_id !== undefined) {
      const zoneId = Math.floor(params.zone_id);
      if (zoneId > 0) {
        searchParams.append('zone_id', zoneId.toString());
      }
    }

    // Validate and add search parameter
    if (params.search && typeof params.search === 'string') {
      const search = params.search.trim();
      if (search.length > 0 && search.length <= 100) {
        searchParams.append('search', search);
      }
    }

    // Validate and add status parameter
    if (params.status && ['active', 'inactive', 'all'].includes(params.status)) {
      searchParams.append('status', params.status);
    }

    // Validate and add sort parameters
    if (params.sort_by && ['name', 'zone_name', 'rating', 'created_at'].includes(params.sort_by)) {
      searchParams.append('sort_by', params.sort_by);
    }

    if (params.sort_order && ['asc', 'desc'].includes(params.sort_order)) {
      searchParams.append('sort_order', params.sort_order);
    }

    return searchParams;
  }

  /**
   * Fetch all hotels with optional filters
   */
  async getAllHotels(params: HotelsQueryParams = {}): Promise<HotelResponse> {
    try {
      const searchParams = this.validateAndSanitizeParams(params);
      const qs = searchParams.toString();
      const base = `${API_ENDPOINTS.HOTELS.ALL}${qs ? `?${qs}` : ''}`;
  const url = base + (import.meta?.env?.DEV ? (base.includes('?') ? '&' : '?') + `_ts=${Date.now()}` : '');
      if (import.meta?.env?.DEV) {
        console.debug('HotelsService URL:', url);
      }

      // Debug logging para hotels service
      if (params.search) {
        console.log('🌐 HotelsService Debug:', {
          searchTerm: params.search,
          fullUrl: url,
          params,
          searchParams: Object.fromEntries(searchParams.entries())
        });
      }

      const response = await fetch(url, this.createFetchOptions({
        method: 'GET',
        cache: 'no-store',
      }));
      
      // Debug logging para response status
      if (import.meta?.env?.DEV) {
        console.debug('🔗 Hotels Response Status:', {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries())
        });
      }

      // Leer cuerpo como texto para medir longitud real
      const text = await response.text();
      if (!response.ok) {
        const msg = `HTTP error! status: ${response.status} - ${response.statusText}${text ? ' | ' + text : ''}`;
        throw new Error(msg);
      }

      if (!text) {
        console.warn('HotelsService: Empty response body (0 B)', { url, status: response.status });
        // Intenta GET de health para diagnosticar
        try {
          const health = await fetch(API_ENDPOINTS.HEALTH, { cache: 'no-store' });
          console.info('Health status:', health.status);
        } catch (e) {
          console.warn('Health check fetch failed', e);
        }
        // Construye una respuesta vacía válida para no romper UI (formato HotelResponse)
        return {
          success: true,
          hotels: [],
          total_hotels: 0,
          message: 'Empty response from server'
        } as unknown as HotelResponse;
      }
      if (import.meta?.env?.DEV) {
        console.debug('📏 Hotels text length:', text.length);
      }
      const parsed: unknown = JSON.parse(text);
      // Type guard for ApiHotelResponse
      const isApiHotelResponse = (v: unknown): v is ApiHotelResponse => {
        return (
          typeof v === 'object' && v !== null &&
          'success' in v &&
          'hotels' in v && Array.isArray((v as { hotels: unknown }).hotels)
        );
      };
      if (!isApiHotelResponse(parsed)) {
        throw new Error('Invalid response structure from hotels API');
      }
      const data = parsed;

      // Debug logging para response
      if (params.search) {
        console.log('📦 HotelsService Response:', {
          searchTerm: params.search,
          success: data.success,
          totalHotels: data.total_hotels,
          hotelsReceived: data.hotels?.length || 0,
          hotels: data.hotels?.map(h => ({ id_hotel: h.id_hotel, nombre: h.nombre, zone_name: h.zone_name })) || []
        });
      }

  // Validate response structure
  // data is valid ApiHotelResponse at this point

      // Transform API hotel data to frontend format
      const transformedHotels = data.hotels.map((apiHotel: ApiHotel) => this.transformHotelData(apiHotel));
      
      // Validate each transformed hotel object
      const validHotels = transformedHotels.filter(hotel => this.validateHotel(hotel));
      
      if (validHotels.length !== transformedHotels.length) {
        console.warn('Some hotels were filtered out due to invalid structure after transformation');
      }

      // Debug logging para hotels transformed
      if (params.search) {
        console.log('🔄 Transformed Hotels:', {
          searchTerm: params.search,
          originalCount: data.hotels.length,
          transformedCount: transformedHotels.length,
          validCount: validHotels.length,
          sampleTransformed: validHotels.slice(0, 3).map(h => ({ id: h.id, name: h.name, zone_id: h.zone_id, zone_name: h.zone_name }))
        });
      }

  return {
        ...data,
        hotels: validHotels,
        total_hotels: data.total_hotels || validHotels.length,
      };
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(ERROR_MESSAGES.TIMEOUT);
        }
        throw error;
      }
      throw new Error(ERROR_MESSAGES.NETWORK);
    }
  }

  /**
   * Fetch hotels by zone ID
   */
  async getHotelsByZone(params: HotelsByZoneParams): Promise<HotelByZoneResponse> {
    try {
      // Validate zone_id parameter
      const zoneId = Math.floor(params.zone_id);
      if (zoneId <= 0) {
        throw new Error('Invalid zone ID: must be a positive number');
      }

      const searchParams = new URLSearchParams();
      
      // Add optional parameters
      if (params.page !== undefined) {
        const page = Math.max(1, Math.floor(params.page));
        searchParams.append('page', page.toString());
      }

      if (params.limit !== undefined) {
        const limit = Math.max(1, Math.min(100, Math.floor(params.limit)));
        searchParams.append('limit', limit.toString());
      }

      if (params.search && typeof params.search === 'string') {
        const search = params.search.trim();
        if (search.length > 0 && search.length <= 100) {
          searchParams.append('search', search);
        }
      }

      if (params.status && ['active', 'inactive', 'all'].includes(params.status)) {
        searchParams.append('status', params.status);
      }

      const base = `${API_ENDPOINTS.HOTELS.BY_ZONE(zoneId)}?${searchParams.toString()}`;
      const url = base + (import.meta?.env?.DEV ? (base.includes('?') ? '&' : '?') + `_ts=${Date.now()}` : '');

      if (import.meta?.env?.DEV) {
        console.debug('HotelsService BY_ZONE URL:', url);
      }

      const response = await fetch(url, this.createFetchOptions({
        method: 'GET',
        cache: 'no-store',
      }));

      // Log status in dev
      if (import.meta?.env?.DEV) {
        console.debug('🔗 Hotels By Zone Response Status:', {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
        });
      }

      const text = await response.text();
      if (!response.ok) {
        const msg = `HTTP error! status: ${response.status} - ${response.statusText}${text ? ' | ' + text : ''}`;
        throw new Error(msg);
      }

      if (!text) {
        console.warn('HotelsService: Empty response body (0 B) for by-zone', { url, status: response.status });
        try {
          const health = await fetch(API_ENDPOINTS.HEALTH, { cache: 'no-store' });
          console.info('Health status:', health.status);
        } catch (e) {
          console.warn('Health check fetch failed', e);
        }
        return {
          success: true,
          hotels: [],
          total_hotels: 0,
          zone_id: zoneId,
          zone_name: '',
          message: 'Empty response from server',
        } as HotelByZoneResponse;
      }

      if (import.meta?.env?.DEV) {
        console.debug('📏 Hotels By Zone text length:', text.length);
      }

      const parsed: unknown = JSON.parse(text);
      // Minimal structural validation
      const isApiHotelResponse = (v: unknown): v is ApiHotelResponse => {
        return (
          typeof v === 'object' && v !== null &&
          'success' in v &&
          'hotels' in v && Array.isArray((v as { hotels: unknown }).hotels)
        );
      };
      if (!isApiHotelResponse(parsed)) {
        throw new Error('Invalid response structure from hotels by zone API');
      }
  const data: ApiHotelResponse = parsed;

      // Transform and validate
      const transformedHotels = data.hotels.map((apiHotel: ApiHotel) => this.transformHotelData(apiHotel));
      const validHotels = transformedHotels.filter(hotel => this.validateHotel(hotel));
      if (validHotels.length !== transformedHotels.length) {
        console.warn('Some hotels were filtered out due to invalid structure after transformation');
      }

      return {
  success: data.success,
  hotels: validHotels,
  zone_id: zoneId,
  zone_name: validHotels[0]?.zone_name ?? '',
  total_hotels: data.total_hotels || validHotels.length,
  message: data.message,
      };
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(ERROR_MESSAGES.TIMEOUT);
        }
        throw error;
      }
      throw new Error(ERROR_MESSAGES.NETWORK);
    }
  }

  /**
   * Retry mechanism for failed requests
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    maxAttempts: number = API_CONFIG.RETRY_ATTEMPTS
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(ERROR_MESSAGES.UNKNOWN);
        
        // Don't retry on certain errors
        const apiError = lastError as ApiError;
        if (
          lastError.message.includes('Invalid') ||
          lastError.message.includes('timeout') ||
          apiError.status === 404 ||
          apiError.status === 400
        ) {
          throw lastError;
        }

        if (attempt < maxAttempts) {
          await new Promise(resolve => 
            setTimeout(resolve, API_CONFIG.RETRY_DELAY * attempt)
          );
        }
      }
    }

    throw lastError!;
  }

  /**
   * Get hotels with automatic retry
   */
  async getAllHotelsWithRetry(params: HotelsQueryParams = {}): Promise<HotelResponse> {
    return this.withRetry(() => this.getAllHotels(params));
  }

  /**
   * Get hotels by zone with automatic retry
   */
  async getHotelsByZoneWithRetry(params: HotelsByZoneParams): Promise<HotelByZoneResponse> {
    return this.withRetry(() => this.getHotelsByZone(params));
  }
}

// Export singleton instance
export const hotelsService = new HotelsService();
export default hotelsService;