import { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import type { Hotel } from '@/features/hotels';

// Tipos para información del aeropuerto
interface AirportInfo {
  id: string;
  name: string;
  code: string;
  city?: string;
}

// Estado de la reserva
interface BookingState {
  from: string;
  to: string;
  date: Date | null;
  time: string;
  returnDate: Date | null;
  returnTime: string;
  roundTrip: boolean;
  adults: string;
  kids: string;
  fromAirportInfo?: AirportInfo;
  toAirportInfo?: AirportInfo;
  selectedHotel?: Hotel;
  contactInfo?: ContactInfo;
  flightInfo?: FlightInfo;
  additionalItems?: AdditionalItems;
  selectedService?: SelectedService;
}

// Exportar tipos para uso en otros componentes
export type { ContactInfo, FlightInfo, AdditionalItems, ExtraServices, BookingState, AirportInfo };

// Tipos para información de contacto
interface ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  confirmEmail: string;
  phone: string;
  countryCode: string;
}

// Tipos para información de vuelo (campos opcionales)
interface FlightInfo {
  arrivalTime?: string;
  airline?: string;
  flightNumber?: string;
}

// Tipos para servicios adicionales
interface ExtraServices {
  stopShop: boolean;
  golfBags: boolean;
  surfboards: boolean;
}

interface AdditionalItems {
  babySeat: string;
  carSeat: string;
  boosterSeat: string;
  specialInstructions?: string;
  extraServices: ExtraServices;
}

// Información del servicio seleccionado
interface SelectedService {
  id: number;
  name: string;
  image: string;
  price: number;
  currency: string;
  features: string[];
  capacity: number;
  type: string;
  note?: string;
  isPopular?: boolean;
}

// Estado principal de la reserva
interface BookingState {
  // Datos básicos del transfer
  from: string;
  to: string;
  date: Date | null;
  time: string;
  returnDate: Date | null;
  returnTime: string;
  adults: string;
  kids: string;
  roundTrip: boolean;
  selectedHotel?: Hotel;
  // Estado de la reserva
  isBookingComplete: boolean;

  // Servicio seleccionado
  selectedService?: SelectedService | null;

  // Información detallada del aeropuerto de origen
  fromAirportInfo?: AirportInfo;

  // Datos de la reserva completa
  contactInfo?: ContactInfo;
  flightInfo?: FlightInfo;
  additionalItems?: AdditionalItems;

}

type BookingAction =
  | { type: 'SET_FORM_DATA'; payload: Partial<BookingState> }
  | { type: 'SET_HOTEL'; payload: Hotel | null }
  | { type: 'SET_ROUND_TRIP'; payload: boolean }
  | { type: 'SET_SELECTED_SERVICE'; payload: SelectedService | null }
  | { type: 'SET_FROM_AIRPORT'; payload: AirportInfo }
  | { type: 'SET_CONTACT_INFO'; payload: ContactInfo }
  | { type: 'SET_FLIGHT_INFO'; payload: FlightInfo }
  | { type: 'SET_ADDITIONAL_ITEMS'; payload: AdditionalItems }
  | { type: 'SET_BOOKING_COMPLETE'; payload: boolean }
  | { type: 'LOAD_PERSISTED_DATA'; payload: BookingState }
  | { type: 'RESET' };

interface BookingContextType {
  state: BookingState;
  dispatch: React.Dispatch<BookingAction>;
  updateFormData: (data: Partial<BookingState>) => void;
  setSelectedHotel: (hotel: Hotel | null) => void;
  setRoundTrip: (isRoundTrip: boolean) => void;
  setSelectedService: (service: SelectedService | null) => void;
  setFromAirport: (airportInfo: AirportInfo) => void;
  setContactInfo: (contactInfo: ContactInfo) => void;
  setFlightInfo: (flightInfo: FlightInfo) => void;
  setAdditionalItems: (additionalItems: AdditionalItems) => void;
  setBookingComplete: (isComplete: boolean) => void;
  resetBooking: () => void;
  // Helper functions
  getZoneId: () => number | null;
  getDestinationInfo: () => {
    type: 'hotel' | 'custom_address';
    zone_id: number | null;
    zone_name: string | null;
    hotel_id: number | null;
    hotel_name: string | null;
    address: string | null;
    destination_text: string;
  };
  // Función para obtener datos completos de la reserva
  getCompleteBookingData: () => BookingState;
}

// Valores iniciales
const initialState: BookingState = {
  from: '',
  to: '',
  date: null,
  time: '',
  returnDate: null,
  returnTime: '',
  adults: '1',
  kids: '0',
  roundTrip: true,
  selectedHotel: undefined,
  selectedService: null,
  fromAirportInfo: undefined,
  contactInfo: undefined,
  flightInfo: undefined,
  additionalItems: undefined,
  isBookingComplete: false
};

// Reducer
function bookingReducer(state: BookingState, action: BookingAction): BookingState {
  switch (action.type) {
    case 'SET_FORM_DATA':
      return {
        ...state,
        ...action.payload
      };
    case 'SET_HOTEL':
      return {
        ...state,
        selectedHotel: action.payload
        // zoneId se obtiene dinámicamente de selectedHotel?.zone_id
      };
    case 'SET_ROUND_TRIP':
      return {
        ...state,
        roundTrip: action.payload
      };
    case 'SET_SELECTED_SERVICE':
      return {
        ...state,
        selectedService: action.payload
      };
    case 'SET_FROM_AIRPORT':
      return {
        ...state,
        fromAirportInfo: action.payload
      };
    case 'SET_CONTACT_INFO':
      return {
        ...state,
        contactInfo: action.payload
      };
    case 'SET_FLIGHT_INFO':
      return {
        ...state,
        flightInfo: action.payload
      };
    case 'SET_ADDITIONAL_ITEMS':
      return {
        ...state,
        additionalItems: action.payload
      };
    case 'SET_BOOKING_COMPLETE':
      return {
        ...state,
        isBookingComplete: action.payload
      };
    case 'LOAD_PERSISTED_DATA':
      return {
        ...state,
        ...action.payload
      };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

// Crear el contexto
const BookingContext = createContext<BookingContextType | undefined>(undefined);

// Storage key
const STORAGE_KEY = 'bajatravel-booking-session';

// Provider Component
export function BookingProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(bookingReducer, initialState);

  // Cargar datos persistidos al montar
  useEffect(() => {
    try {
      const persistedData = sessionStorage.getItem(STORAGE_KEY);
      if (persistedData) {
        const parsed = JSON.parse(persistedData);
        // Convertir la fecha string a objeto Date si existe
        if (parsed.date) {
          parsed.date = new Date(parsed.date);
        }
        // Convertir la fecha de retorno string a objeto Date si existe
        if (parsed.returnDate) {
          parsed.returnDate = new Date(parsed.returnDate);
        }
        dispatch({ type: 'LOAD_PERSISTED_DATA', payload: parsed });
      }
    } catch (error) {
      console.error('Error loading persisted booking data:', error);
    }
  }, []);

  // Persistir cambios
  useEffect(() => {
    try {
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.error('Error persisting booking data:', error);
    }
  }, [state]);

  // Acciones simplificadas
  const updateFormData = (data: Partial<BookingState>) => {
    dispatch({ type: 'SET_FORM_DATA', payload: data });
  };

  const setSelectedHotel = (hotel: Hotel | null) => {
    dispatch({ type: 'SET_HOTEL', payload: hotel });
  };

  const setRoundTrip = (isRoundTrip: boolean) => {
    dispatch({ type: 'SET_ROUND_TRIP', payload: isRoundTrip });
  };

  const setSelectedService = (service: SelectedService | null) => {
    dispatch({ type: 'SET_SELECTED_SERVICE', payload: service });
  };

  const setFromAirport = (airportInfo: AirportInfo) => {
    dispatch({ type: 'SET_FROM_AIRPORT', payload: airportInfo });
  };

  const setContactInfo = (contactInfo: ContactInfo) => {
    dispatch({ type: 'SET_CONTACT_INFO', payload: contactInfo });
  };

  const setFlightInfo = (flightInfo: FlightInfo) => {
    dispatch({ type: 'SET_FLIGHT_INFO', payload: flightInfo });
  };

  const setAdditionalItems = (additionalItems: AdditionalItems) => {
    dispatch({ type: 'SET_ADDITIONAL_ITEMS', payload: additionalItems });
  };

  const setBookingComplete = (isComplete: boolean) => {
    dispatch({ type: 'SET_BOOKING_COMPLETE', payload: isComplete });
  };

  const resetBooking = () => {
    dispatch({ type: 'RESET' });
  };

  // Helper functions para evitar duplicación de datos
  const getZoneId = (): number | null => {
    return state.selectedHotel?.zone_id || null;
  };

  const getDestinationInfo = () => {
    if (state.selectedHotel) {
      return {
        type: 'hotel' as const,
        zone_id: state.selectedHotel.zone_id,
        zone_name: state.selectedHotel.zone_name,
        hotel_id: state.selectedHotel.id,
        hotel_name: state.selectedHotel.name,
        address: state.selectedHotel.address,
        destination_text: state.to
      };
    } else {
      return {
        type: 'custom_address' as const,
        zone_id: null,
        zone_name: null,
        hotel_id: null,
        hotel_name: null,
        address: state.to,
        destination_text: state.to
      };
    }
  };

  // Función para obtener datos completos de la reserva
  const getCompleteBookingData = (): BookingState => {
    return state;
  };

  return (
    <BookingContext.Provider
      value={{
        state,
        dispatch,
        updateFormData,
        setSelectedHotel,
        setRoundTrip,
        setSelectedService,
        setFromAirport,
        setContactInfo,
        setFlightInfo,
        setAdditionalItems,
        setBookingComplete,
        resetBooking,
        getZoneId,
        getDestinationInfo,
        getCompleteBookingData
      }}
    >
      {children}
    </BookingContext.Provider>
  );
}

// Hook personalizado para usar el contexto
export function useBooking() {
  const context = useContext(BookingContext);
  if (context === undefined) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
}
