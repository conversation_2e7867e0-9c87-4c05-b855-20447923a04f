import { API_ENDPOINTS, API_CONFIG, APP_CONFIG, isDevelopment } from '../../../config/config';
import { Airport } from '../types/airport.types';

/**
 * Legacy service for airports - now simplified for React Query
 * This service is kept for backward compatibility but React Query hooks are preferred
 */
class AirportsService {
  private baseUrl = API_ENDPOINTS.AIRPORTS.ALL;

  /**
   * Fetch all airports - used internally by React Query
   */
  async getAllAirports(): Promise<Airport[]> {
    try {
  const url = this.baseUrl + ((isDevelopment || APP_CONFIG.FEATURES.ENABLE_DEBUG) ? (this.baseUrl.includes('?') ? '&' : '?') + `_ts=${Date.now()}` : '');
      const response = await fetch(url, {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
        cache: 'no-store',
        credentials: 'omit',
        signal: AbortSignal.timeout(API_CONFIG.TIMEOUT),
      });

      if (!response.ok) {
        const text = await response.text().catch(() => '');
        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}${text ? ' | ' + text : ''}`);
      }
      const raw = await response.text();
      if (!raw) {
        // Empty body (0 B) fallback
        console.warn('AirportsService: Empty response body (0 B) from', url);
        return [];
      }
      const data: unknown = JSON.parse(raw);
      
      // Type guard for the response
      const isValidResponse = (response: unknown): response is { success?: boolean; data?: Airport[]; message?: string } => {
        return typeof response === 'object' && response !== null;
      };
      
  if (!isValidResponse(data)) {
        throw new Error('Invalid API response format');
      }
      
      // Check if response has success property and it's false
      if ('success' in data && data.success === false) {
        throw new Error(data.message || 'Failed to fetch airports');
      }

      // Return data.data if it exists, otherwise try data directly, otherwise empty array
      let airports: Airport[] = [];
      if (data.data && Array.isArray(data.data)) {
        airports = data.data;
      } else if (Array.isArray(data)) {
        airports = data as Airport[];
      }
      
  return airports;
    } catch (error) {
      console.error('Error fetching airports:', error);
      throw error;
    }
  }

  /**
   * Search airports by name, nombre or city
   * @deprecated Use useAirportSearch or useAirportsWithLocalSearch hooks instead
   */
  async searchAirports(query: string): Promise<Airport[]> {
    const airports = await this.getAllAirports();
    const searchTerm = query.toLowerCase().trim();
    
    return airports.filter(airport => 
      airport.name.toLowerCase().includes(searchTerm) ||
      airport.nombre.toLowerCase().includes(searchTerm) ||
      airport.city.toLowerCase().includes(searchTerm)
    );
  }
}

export const airportsService = new AirportsService();
export default airportsService;
