import { useMemo, useCallback, useEffect } from 'react';
import { activeTheme, generateCSSVariables, type ThemeConfig } from '@/config/theme';

/**
 * Theme variant types
 */
export type ThemeVariant = 'light' | 'dark' | 'auto';

/**
 * Theme hook return interface
 */
export interface UseThemeReturn {
  theme: ThemeConfig;
  cssVariables: Record<string, string>;
  applyThemeStyles: () => void;
  resetThemeStyles: () => void;
  updateThemeVariable: (property: string, value: string) => void;
  // Helpers for quick access
  colors: ThemeConfig['colors'];
  gradients: ThemeConfig['gradients'];
  logos: ThemeConfig['logos'];
  company: ThemeConfig['company'];
  bookingLayout: ThemeConfig['bookingLayout'];
}

/**
 * Hook for managing application theme
 * Provides theme configuration, CSS variables, and helper functions
 */
export const useTheme = (): UseThemeReturn => {
  const theme = useMemo(() => activeTheme, []);
  
  const cssVariables = useMemo(() => generateCSSVariables(theme), [theme]);
  
  /**
   * Apply theme styles to document root
   */
  const applyThemeStyles = useCallback(() => {
    const root = document.documentElement;
    Object.entries(cssVariables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }, [cssVariables]);

  /**
   * Reset theme styles from document root
   */
  const resetThemeStyles = useCallback(() => {
    const root = document.documentElement;
    Object.keys(cssVariables).forEach((property) => {
      root.style.removeProperty(property);
    });
  }, [cssVariables]);

  /**
   * Update a specific theme variable
   */
  const updateThemeVariable = useCallback((property: string, value: string) => {
    const root = document.documentElement;
    root.style.setProperty(property, value);
  }, []);

  // Auto-apply theme styles when component mounts
  useEffect(() => {
    applyThemeStyles();
  }, [applyThemeStyles]);

  return {
    theme,
    cssVariables,
    applyThemeStyles,
    resetThemeStyles,
    updateThemeVariable,
    // Helpers for quick access
    colors: theme.colors,
    gradients: theme.gradients,
    logos: theme.logos,
    company: theme.company,
    bookingLayout: theme.bookingLayout,
  };
};

/**
 * Hook for getting specific theme colors
 */
export const useThemeColors = () => {
  const { colors } = useTheme();
  
  return {
    primary: colors.primary,
    secondary: colors.secondary,
    accent: colors.accent,
    background: colors.background,
    surface: colors.surface,
    // Helper functions for color manipulation
    getPrimaryColor: () => colors.primary,
    getSecondaryColor: () => colors.secondary,
    getAccentColor: () => colors.accent,
    getBackgroundColor: () => colors.background,
    getSurfaceColor: () => colors.surface,
  };
};

/**
 * Hook for getting theme gradients
 */
export const useThemeGradients = () => {
  const { gradients } = useTheme();
  
  return {
    ...gradients,
    // Helper function to create custom gradients
    createGradient: (color1: string, color2: string, direction = 'to right') => 
      `linear-gradient(${direction}, ${color1}, ${color2})`,
  };
};

/**
 * Hook for responsive theme utilities
 */
export const useResponsiveTheme = () => {
  const { theme, updateThemeVariable } = useTheme();
  
  const setResponsiveSpacing = useCallback((size: 'sm' | 'md' | 'lg') => {
    const spacingMap = {
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
    };
    updateThemeVariable('--spacing-unit', spacingMap[size]);
  }, [updateThemeVariable]);

  const setResponsiveFontSize = useCallback((scale: number) => {
    updateThemeVariable('--font-scale', scale.toString());
  }, [updateThemeVariable]);

  return {
    theme,
    setResponsiveSpacing,
    setResponsiveFontSize,
  };
};

export default useTheme;
