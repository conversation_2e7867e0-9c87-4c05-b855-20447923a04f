// Tipos compartidos para los componentes BookingForm

export interface BookingFormData {
  from: string;
  to: string;
  date: Date | null;
  time: string;
  adults: string;
  kids: string;
  roundTrip: boolean;
  zoneId?: string;
}

export interface LegacyBookingFormProps {
  className?: string;
  onSubmit?: (data: BookingFormData) => void;
  onFormChange?: (data: BookingFormData) => void;
  initialData?: Partial<BookingFormData>;
}

export interface ValidationErrors {
  from?: boolean;
  to?: boolean;
  date?: boolean;
  time?: boolean;
  adults?: boolean;
  kids?: boolean;
}

export interface DestinationInfo {
  type: 'hotel' | 'airport' | 'custom';
  zone_id?: number;
  zone_name?: string;
  hotel_id?: number;
  hotel_name?: string;
  address?: string;
  airport_code?: string;
}
