
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useBooking } from '@/context/BookingContext';
import type { Hotel } from '@/features/hotels';
import {
  OriginSelect,
  DestinationSearch,
  DatePicker,
  TimeSelect,
  RoundTripToggle
} from '../components';

const BookingFormHorizontal = () => {
  const { state, updateFormData, setSelectedHotel, setRoundTrip } = useBooking();

  const [formErrors, setFormErrors] = useState({
    from: false,
    to: false,
    date: false,
    time: false
  });

  const validateForm = () => {
    const errors = {
      from: !state.from,
      to: !state.to,
      date: !state.date,
      time: !state.time
    };
    setFormErrors(errors);
    return !Object.values(errors).some(Boolean);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // Implementar lógica de envío si es necesaria
      console.log('Form is valid, submitting...', state);
    } else {
      console.log('Form has errors', formErrors);
    }
  };

  const handleHotelChange = (value: string, hotel?: Hotel) => {
    if (hotel) {
      // Asegurarse de que tenemos todos los campos necesarios
      const completeHotel: Hotel = {
        ...hotel,
        id: hotel.id,
        name: hotel.name,
        zone_id: hotel.zone_id,
        zone_name: hotel.zone_name || '',
        address: value // Usar el valor como dirección si no hay una
      };
      console.log('Setting hotel with data:', completeHotel);
      updateFormData({ to: value });
      setSelectedHotel(completeHotel);
    } else {
      // Si no hay hotel, actualizar solo el valor del campo
      updateFormData({ to: value });
      setSelectedHotel(null);
    }
  };

  const airportOptions = [
    { value: "sjd", label: "SJD Airport", city: "Los Cabos" },
    { value: "hotel", label: "Hotel", city: "Pick up" }
  ];

  return (
    <div className="w-full max-w-5xl">
      <form className="bg-white/95 backdrop-blur-sm shadow-2xl rounded-lg p-6" onSubmit={handleSubmit}>
        {/* Header with title and round trip toggle */}
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-bold text-gray-900">
            Book Your Transfer
          </h3>
          
          <div style={{alignContent: 'center'}}>
            <RoundTripToggle
              value={state.roundTrip}
              onChange={setRoundTrip}
            />
          </div>
        </div>
        
        {/* Horizontal Form Fields */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
          <OriginSelect
            value={state.from}
            onChange={(value) => updateFormData({ from: value })}
            error={!state.from}
            airportOptions={airportOptions}
            loading={false}
            placeholder="Select pickup location"
            errorText="Pickup location is required"
            required
          />
          
          <DestinationSearch
            value={state.to}
            onChange={handleHotelChange}
            error={!state.to}
            selectedHotel={state.selectedHotel}
            placeholder="Enter hotel or destination"
            errorText="Destination is required"
            required
          />

          <DatePicker
            value={state.date}
            onChange={(date) => updateFormData({ date })}
            error={!state.date}
            errorText="Date is required"
            required
          />
          
          <TimeSelect
            value={state.time}
            onChange={(value) => updateFormData({ time: value })}
            error={!state.time}
            placeholder="Select pickup time"
            errorText="Pickup time is required"
            required
          />

          {/* Book Now button aligned with fields */}
          <div className="flex items-end">
            <Button 
              type="submit"
              className="bg-gradient-cabo hover:opacity-90 text-white font-semibold h-12 px-8 w-full"
            >
              Book Now
            </Button>
          </div>
        </div>

        {/* Note about passengers */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Vehicle capacity: Up to 14 passengers (select vehicle type in next step)
          </p>
        </div>
      </form>
    </div>
  );
};

export default BookingFormHorizontal;
