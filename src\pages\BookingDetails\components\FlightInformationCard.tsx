import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PlaneIcon } from 'lucide-react';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { TimeSelect } from '@/components/booking-form/components';
import { useBooking } from '@/context/BookingContext';
import { useAirlines } from '@/features/airlines/hooks/useAirlines';
import type { BookingDetailsFormData } from '../schemas/bookingDetailsSchema';

interface FlightInformationCardProps {
  control: Control<BookingDetailsFormData>;
  errors: FieldErrors<BookingDetailsFormData>;
}

const FlightInformationCard = ({ control, errors }: FlightInformationCardProps) => {
  const { state } = useBooking();
  const { data: airlines, loading: airlinesLoading } = useAirlines();
  
  return (
    <Card className="hover-scale bg-white shadow-lg border-0 overflow-hidden group">
      <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 border-b border-primary/20">
        <CardTitle className="flex items-center gap-2 text-primary">
          <PlaneIcon className="w-5 h-5" />
          Flight Information
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-4">
        <div className="grid md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>Arrival Time</Label>
            <Controller
              name="flightInfo.arrivalTime"
              control={control}
              render={({ field }) => (
                <TimeSelect
                  value={field.value || state.time || ''}
                  onChange={field.onChange}
                  error={!!errors.flightInfo?.arrivalTime}
                  errorText="Please select arrival time"
                />
              )}
            />
          </div>
          <div className="space-y-2">
            <Label>Arrival Airline</Label>
            <Controller
              name="flightInfo.airline"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value || ''}
                  onValueChange={field.onChange}
                  disabled={airlinesLoading}
                >
                  <SelectTrigger className={errors.flightInfo?.airline ? 'border-red-500' : ''}>
                    <SelectValue placeholder={airlinesLoading ? 'Loading airlines...' : 'Select airline'} />
                  </SelectTrigger>
                  <SelectContent>
                    {airlines?.map((airline) => (
                      <SelectItem key={airline.code_airline} value={airline.name}>
                        {airline.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
          <div className="space-y-2">
            <Label>Arrival Flight Number</Label>
            <Controller
              name="flightInfo.flightNumber"
              control={control}
              render={({ field }) => (
                <Input
                  placeholder="e.g., AA1234"
                  {...field}
                />
              )}
            />
          </div>
        </div>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>Departure Time</Label>
            <Controller
              name="flightInfo.arrivalTime"
              control={control}
              render={({ field }) => (
                <TimeSelect
                  value={field.value || state.time || ''}
                  onChange={field.onChange}
                  error={!!errors.flightInfo?.arrivalTime}
                  errorText="Please select arrival time"
                />
              )}
            />
          </div>
          <div className="space-y-2">
            <Label>Departure Airline</Label>
            <Controller
              name="flightInfo.airline"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value || ''}
                  onValueChange={field.onChange}
                  disabled={airlinesLoading}
                >
                  <SelectTrigger className={errors.flightInfo?.airline ? 'border-red-500' : ''}>
                    <SelectValue placeholder={airlinesLoading ? 'Loading airlines...' : 'Select airline'} />
                  </SelectTrigger>
                  <SelectContent>
                    {airlines?.map((airline) => (
                      <SelectItem key={airline.code_airline} value={airline.name}>
                        {airline.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
          <div className="space-y-2">
            <Label>Departure Flight Number</Label>
            <Controller
              name="flightInfo.flightNumber"
              control={control}
              render={({ field }) => (
                <Input
                  placeholder="e.g., AA1234"
                  {...field}
                />
              )}
            />
          </div>
        </div>

        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-700">
            💡 Flight information helps us track your arrival and provide better service
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default FlightInformationCard;
