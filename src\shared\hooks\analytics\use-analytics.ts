import { useCallback } from 'react';

/**
 * Global window interface extensions for analytics
 */
declare global {
  interface Window {
    gtag?: (
      command: 'config' | 'event' | 'js' | 'set',
      targetId: string,
      config?: Record<string, unknown>
    ) => void;
    dataLayer?: Record<string, unknown>[];
  }
}

/**
 * Analytics event interface
 */
export interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
  custom_parameters?: Record<string, string | number | boolean>;
}

/**
 * Page view tracking interface
 */
export interface PageViewEvent {
  pagePath: string;
  pageTitle: string;
  userId?: string;
  customDimensions?: Record<string, string | number | boolean>;
}

/**
 * E-commerce tracking interface
 */
export interface EcommerceEvent {
  transaction_id: string;
  value: number;
  currency: string;
  items: Array<{
    item_id: string;
    item_name: string;
    category: string;
    quantity: number;
    price: number;
  }>;
}

/**
 * Hook for analytics tracking functionality
 * Supports Google Analytics 4 and Google Tag Manager
 */
const useAnalytics = () => {
  /**
   * Track custom events
   */
  const trackEvent = useCallback(({ 
    action, 
    category, 
    label, 
    value, 
    custom_parameters = {} 
  }: AnalyticsEvent) => {
    try {
      // Google Analytics 4 tracking
      if (window.gtag) {
        window.gtag('event', action, {
          event_category: category,
          event_label: label,
          value: value,
          ...custom_parameters,
        });
      }

      // Google Tag Manager dataLayer
      if (window.dataLayer) {
        window.dataLayer.push({
          event: 'custom_event',
          event_action: action,
          event_category: category,
          event_label: label,
          event_value: value,
          ...custom_parameters,
        });
      }

      // Console log for debugging in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Analytics Event:', { 
          action, 
          category, 
          label, 
          value, 
          custom_parameters 
        });
      }
    } catch (error) {
      console.error('Error tracking analytics event:', error);
    }
  }, []);

  /**
   * Track page views
   */
  const trackPageView = useCallback(({ 
    pagePath, 
    pageTitle, 
    userId, 
    customDimensions = {} 
  }: PageViewEvent) => {
    try {
      // Google Analytics 4 page view
      if (window.gtag) {
        window.gtag('config', 'GA_MEASUREMENT_ID', {
          page_path: pagePath,
          page_title: pageTitle,
          user_id: userId,
          custom_map: customDimensions,
        });
      }

      // Google Tag Manager page view
      if (window.dataLayer) {
        window.dataLayer.push({
          event: 'page_view',
          page_path: pagePath,
          page_title: pageTitle,
          user_id: userId,
          ...customDimensions,
        });
      }

      // Console log for debugging in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Page View:', { 
          pagePath, 
          pageTitle, 
          userId, 
          customDimensions 
        });
      }
    } catch (error) {
      console.error('Error tracking page view:', error);
    }
  }, []);

  /**
   * Track e-commerce events (purchases, etc.)
   */
  const trackEcommerce = useCallback((event: EcommerceEvent) => {
    try {
      // Google Analytics 4 e-commerce
      if (window.gtag) {
        window.gtag('event', 'purchase', {
          transaction_id: event.transaction_id,
          value: event.value,
          currency: event.currency,
          items: event.items,
        });
      }

      // Google Tag Manager e-commerce
      if (window.dataLayer) {
        window.dataLayer.push({
          event: 'purchase',
          ecommerce: {
            transaction_id: event.transaction_id,
            value: event.value,
            currency: event.currency,
            items: event.items,
          },
        });
      }

      // Console log for debugging in development
      if (process.env.NODE_ENV === 'development') {
        console.log('E-commerce Event:', event);
      }
    } catch (error) {
      console.error('Error tracking e-commerce event:', error);
    }
  }, []);

  /**
   * Predefined tracking functions for common events
   */
  const trackBookingStart = useCallback((service: string) => {
    trackEvent({
      action: 'booking_start',
      category: 'engagement',
      label: service,
    });
  }, [trackEvent]);

  const trackBookingComplete = useCallback((
    service: string, 
    value: number, 
    bookingId: string
  ) => {
    trackEvent({
      action: 'booking_complete',
      category: 'conversion',
      label: service,
      value,
      custom_parameters: { booking_id: bookingId },
    });
  }, [trackEvent]);

  const trackQuoteRequest = useCallback((service: string) => {
    trackEvent({
      action: 'quote_request',
      category: 'lead_generation',
      label: service,
    });
  }, [trackEvent]);

  const trackFormSubmission = useCallback((formName: string) => {
    trackEvent({
      action: 'form_submit',
      category: 'engagement',
      label: formName,
    });
  }, [trackEvent]);

  const trackPhoneClick = useCallback(() => {
    trackEvent({
      action: 'phone_click',
      category: 'contact',
      label: 'header_phone',
    });
  }, [trackEvent]);

  const trackEmailClick = useCallback(() => {
    trackEvent({
      action: 'email_click',
      category: 'contact',
      label: 'header_email',
    });
  }, [trackEvent]);

  return {
    // Core tracking functions
    trackEvent,
    trackPageView,
    trackEcommerce,
    
    // Predefined tracking functions
    trackBookingStart,
    trackBookingComplete,
    trackQuoteRequest,
    trackFormSubmission,
    trackPhoneClick,
    trackEmailClick,
  };
};

export default useAnalytics;
