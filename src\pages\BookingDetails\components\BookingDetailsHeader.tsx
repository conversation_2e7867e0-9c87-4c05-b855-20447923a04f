interface BookingDetailsHeaderProps {
  title?: string;
  subtitle?: string;
}

const BookingDetailsHeader = ({ 
  title = "Complete Your Booking",
  subtitle = "Please fill in the details below to complete your reservation"
}: BookingDetailsHeaderProps) => {
  return (
    <div className="text-center mb-8">
      <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
        {title}
      </h1>
      <p className="text-muted-foreground">
        {subtitle}
      </p>
    </div>
  );
};

export default BookingDetailsHeader;
