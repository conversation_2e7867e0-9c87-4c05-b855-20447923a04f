import { useState } from 'react';
import { HotelSelectSearch } from '@/features/hotels';
import type { Hotel } from '@/features/hotels';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function HotelSearchComparison() {
  const [originalValue, setOriginalValue] = useState('');
  const [newValue, setNewValue] = useState('');
  const [selectedHotel1, setSelectedHotel1] = useState<Hotel | null>(null);
  const [selectedHotel2, setSelectedHotel2] = useState<Hotel | null>(null);

  const handleOriginalChange = (value: string, hotel?: Hotel) => {
    setOriginalValue(value);
    setSelectedHotel1(hotel || null);
  };

  const handleNewChange = (value: string, hotel?: Hotel) => {
    setNewValue(value);
    setSelectedHotel2(hotel || null);
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Hotel Search Components Comparison</h1>
        <p className="text-muted-foreground">
          Comparing the original HotelSearch vs the new HotelSelectSearch with Radix UI
        </p>
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-800">
            ✅ <strong>HotelSelectSearch is now implemented in BookingForm!</strong> 
            Check the main page and destination pages to see it in action.
          </p>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        {/* Original Component */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Search Mode 
              <Badge variant="default">Hero/Search</Badge>
            </CardTitle>
            <CardDescription>
              Input with dynamic search (for Hero sections)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <HotelSelectSearch
              value={originalValue}
              onChange={handleOriginalChange}
              placeholder="Search hotels..."
              destinationZoneId={1} // San José del Cabo
              searchMode="search"
            />
            
            {selectedHotel1 && (
              <div className="p-3 bg-muted rounded-md">
                <p className="font-medium">{selectedHotel1.name}</p>
                <p className="text-sm text-muted-foreground">
                  Zone: {selectedHotel1.zone_name || 'Unknown'}
                </p>
                <p className="text-sm text-muted-foreground">
                  ID: {selectedHotel1.id}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* New Component */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Dropdown Mode 
              <Badge variant="outline">Destination Pages</Badge>
            </CardTitle>
            <CardDescription>
              Select dropdown (for destination-specific pages)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <HotelSelectSearch
              value={newValue}
              onChange={handleNewChange}
              placeholder="Select hotel from list..."
              destinationZoneId={1} // San José del Cabo
              searchMode="dropdown"
            />
            
            {selectedHotel2 && (
              <div className="p-3 bg-muted rounded-md">
                <p className="font-medium">{selectedHotel2.name}</p>
                <p className="text-sm text-muted-foreground">
                  Zone: {selectedHotel2.zone_name || 'Unknown'}
                </p>
                <p className="text-sm text-muted-foreground">
                  ID: {selectedHotel2.id}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Both Mode Example */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Both Modes Combined 
            <Badge variant="secondary">Default</Badge>
          </CardTitle>
          <CardDescription>
            Search input + dropdown selector (when searchMode="both")
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <HotelSelectSearch
            value=""
            onChange={(value, hotel) => console.log('Both mode:', value, hotel)}
            placeholder="Search or select hotel..."
            destinationZoneId={1} // San José del Cabo
            searchMode="both"
          />
        </CardContent>
      </Card>

      {/* Features Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Features Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">Search Mode (Hero)</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Dynamic search input
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Real-time API search
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Best for open-ended search
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Clean, minimal interface
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Dropdown Mode (Destinations)</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Pre-filtered hotel list
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Zone-specific results
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Faster selection
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Radix UI accessibility
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Use</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Usage Examples:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li><strong>Hero sections:</strong> Use <code>searchMode="search"</code> for clean search input</li>
              <li><strong>Destination pages:</strong> Use <code>searchMode="dropdown"</code> for pre-filtered hotels</li>
              <li><strong>General forms:</strong> Use <code>searchMode="both"</code> for maximum flexibility</li>
              <li>Automatically handles zone filtering and hotel availability</li>
            </ol>
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="font-semibold mb-2 text-blue-800">Implementation Status:</h4>
            <ul className="space-y-1 text-sm text-blue-700">
              <li>✅ Hero BookingForm: <code>searchMode="search"</code></li>
              <li>✅ Destination Detail: <code>searchMode="dropdown"</code></li>
              <li>✅ All other forms: <code>searchMode="both"</code> (default)</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
