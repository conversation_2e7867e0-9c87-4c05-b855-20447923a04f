# Features Architecture

Esta estructura está inspirada en la arquitectura de NestJS y organiza el código por features/módulos para una mejor escalabilidad y mantenimiento.

## Estructura de Carpetas

```
src/
├── features/
│   ├── airports/              # Módulo de aeropuertos
│   │   ├── types/             # Tipos TypeScript
│   │   │   └── airport.types.ts
│   │   ├── services/          # Servicios de API
│   │   │   └── airports.service.ts
│   │   ├── hooks/             # Custom React Hooks
│   │   │   └── useAirports.ts
│   │   └── index.ts           # Barrel exports
│   ├── booking/               # Módulo de reservas (futuro)
│   └── transportation/        # Módulo de transporte (futuro)
├── shared/                    # Código compartido
│   ├── types/
│   ├── services/
│   ├── hooks/
│   └── utils/
└── components/                # Componentes de UI
    └── ui/
```

## Ventajas de esta Estructura

### 1. **Escalabilidad**
- Cada feature es independiente
- Fácil agregar nuevos módulos
- Separación clara de responsabilidades

### 2. **Mantenibilidad**
- Código organizado por dominio de negocio
- Fácil encontrar y modificar funcionalidad específica
- Reducción de acoplamiento entre módulos

### 3. **Reutilización**
- Barrel exports facilitan importaciones
- Hooks y servicios reutilizables
- Tipos compartidos entre módulos

### 4. **Testabilidad**
- Cada módulo puede testearse independientemente
- Mocking más fácil de servicios específicos
- Tests organizados por feature

## Cómo Usar

### Importar desde un módulo completo:
```typescript
import { useAirports, Airport, airportsService } from '@/features/airports';
```

### Importar elementos específicos:
```typescript
import { useAirports } from '@/features/airports/hooks/useAirports';
import { Airport } from '@/features/airports/types/airport.types';
```

## Próximos Módulos

### Booking Module
- Tipos de reservas
- Servicios de reserva
- Hooks para manejo de estado de reservas

### Transportation Module
- Tipos de vehículos
- Servicios de flota
- Hooks para gestión de transporte

### Users Module (opcional)
- Autenticación
- Perfil de usuario
- Gestión de sesiones

## Convenciones

1. **Naming**: Usar camelCase para archivos y carpetas
2. **Exports**: Usar barrel exports en index.ts
3. **Types**: Sufijo `.types.ts` para archivos de tipos
4. **Services**: Sufijo `.service.ts` para servicios
5. **Hooks**: Prefijo `use` para custom hooks
