// Ejemplos de uso de los nuevos hooks refactorizados para React Query

import React from 'react';
import { 
  useAirports, 
  useAirportSearch, 
  useAirportsWithLocalSearch 
} from '@/features/airports';

/**
 * Ejemplo 1: Hook básico para obtener todos los aeropuertos
 */
export const AirportsListExample = () => {
  const { 
    airports, 
    airportOptions, 
    loading, 
    error, 
    refetch,
    isLoading,
    isError,
    isSuccess 
  } = useAirports();

  if (isLoading) return <div>Loading airports...</div>;
  if (isError) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>All Airports ({airports.length})</h2>
      <button onClick={() => refetch()}>Refresh</button>
      
      <select>
        {airportOptions.map(option => (
          <option key={option.value} value={option.value}>
            {option.label} - {option.city}
          </option>
        ))}
      </select>
    </div>
  );
};

/**
 * Ejemplo 2: Hook de búsqueda con query específico
 */
export const AirportSearchExample = () => {
  const [searchQuery, setSearchQuery] = React.useState('');
  
  const { 
    data: searchResults = [], 
    isLoading, 
    isError, 
    error 
  } = useAirportSearch(searchQuery);

  return (
    <div>
      <h2>Airport Search</h2>
      <input
        type="text"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        placeholder="Search airports..."
      />
      
      {isLoading && <div>Searching...</div>}
      {isError && <div>Error: {String(error)}</div>}
      
      <div>
        {searchResults.map(airport => (
          <div key={airport.id_airport}>
            {airport.name} - {airport.city}
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Ejemplo 3: Hook optimizado para búsqueda local (más eficiente)
 */
export const AirportsWithLocalSearchExample = () => {
  const [searchQuery, setSearchQuery] = React.useState('');
  
  const { 
    airports, 
    allAirports, 
    isLoading, 
    isError, 
    error, 
    isSearching,
    refetch 
  } = useAirportsWithLocalSearch(searchQuery);

  return (
    <div>
      <h2>Airports with Local Search</h2>
      <input
        type="text"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        placeholder="Search airports locally..."
      />
      
      <div>
        {isLoading && <div>Loading...</div>}
        {isError && <div>Error: {String(error)}</div>}
        
        <p>
          {isSearching 
            ? `Found ${airports?.length || 0} airports matching "${searchQuery}"` 
            : `Total airports: ${allAirports?.length || 0}`
          }
        </p>
        
        <button onClick={() => refetch()}>Refresh Data</button>
        
        <div>
          {airports?.map(airport => (
            <div key={airport.id_airport} className="border p-2 m-1">
              <strong>{airport.name}</strong> - {airport.city}
              {airport.nombre && <div>Spanish: {airport.nombre}</div>}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * Ejemplo 4: Hook con opciones personalizadas de React Query
 */
export const AirportsWithCustomOptionsExample = () => {
  const { 
    airports, 
    isLoading, 
    error,
    isFetching 
  } = useAirports({
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 20 * 60 * 1000, // 20 minutes
    refetchOnWindowFocus: true,
    retry: 3,
    retryDelay: 2000,
    // Note: onSuccess y onError fueron deprecados en React Query v5
    // Usa useEffect para side effects si necesitas
  });

  return (
    <div>
      <h2>Airports with Custom Options</h2>
      {isLoading && <div>Initial loading...</div>}
      {isFetching && !isLoading && <div>Refreshing...</div>}
      {error && <div>Error: {error}</div>}
      
      <div>Total airports: {airports.length}</div>
    </div>
  );
};
