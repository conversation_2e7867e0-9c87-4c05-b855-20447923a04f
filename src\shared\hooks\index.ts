// UI Hooks
export { useIsMobile, useViewport, useBreakpoints } from './ui/use-mobile';
export { useToast, toast, toastVariants, type ToasterToast } from './ui/use-toast';

// Analytics Hooks
export { default as useAnalytics } from './analytics/use-analytics';
export type { AnalyticsEvent, PageViewEvent, EcommerceEvent } from './analytics/use-analytics';

// Theme Hooks
export { useTheme, useThemeColors, useThemeGradients, useResponsiveTheme } from './theme/use-theme';
export type { ThemeVariant, UseThemeReturn } from './theme/use-theme';

// Re-export for backward compatibility
export { useTheme as default } from './theme/use-theme';
