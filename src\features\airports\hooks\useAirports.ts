import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { useMemo } from 'react';
import { Airport, AirportSelectOption } from '../types/airport.types';
import { API_ENDPOINTS, API_CONFIG } from '../../../config/config';

// Query Keys
export const airportsKeys = {
  all: ['airports'] as const,
  lists: () => [...airportsKeys.all, 'list'] as const,
  search: (query: string) => [...airportsKeys.all, 'search', query] as const,
};

// API Functions
const fetchAirports = async (): Promise<Airport[]> => {
  const base = API_ENDPOINTS.AIRPORTS.ALL;
  const url = base + (base.includes('?') ? '&' : '?') + `_ts=${Date.now()}`;
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
    },
    cache: 'no-store',
    signal: AbortSignal.timeout(API_CONFIG.TIMEOUT),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
  }

  const data: unknown = await response.json();
  
  // Type guard for the response
  const isValidResponse = (response: unknown): response is { success?: boolean; data?: Airport[]; message?: string } => {
    return typeof response === 'object' && response !== null;
  };
  
  if (!isValidResponse(data)) {
    throw new Error('Invalid API response format');
  }
  
  // Check if response has success property and it's false
  if ('success' in data && data.success === false) {
    throw new Error(data.message || 'Failed to fetch airports');
  }

  // Return data.data if it exists, otherwise try data directly, otherwise empty array
  let airports: Airport[] = [];
  if (data.data && Array.isArray(data.data)) {
    airports = data.data;
  } else if (Array.isArray(data)) {
    airports = data as Airport[];
  }
  
  return airports;
};

interface UseAirportsReturn {
  airports: Airport[];
  airportOptions: AirportSelectOption[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<unknown>;
  searchAirports: (query: string) => Promise<Airport[]>;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  isFetching: boolean;
}

/**
 * Custom hook for managing airports data with React Query
 */
export const useAirports = (options?: Omit<UseQueryOptions<Airport[], Error>, 'queryKey' | 'queryFn'>): UseAirportsReturn => {
  const {
    data: airports = [],
    isLoading,
    isError,
    isSuccess,
    isFetching,
    error,
    refetch,
  } = useQuery({
    queryKey: airportsKeys.lists(),
    queryFn: fetchAirports,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false,
    retry: 2,
    retryDelay: 1000,
    ...options,
  });

  /**
   * Transform airports to select options with data validation
   */
  const airportOptions: AirportSelectOption[] = useMemo(() => {
    return airports
      // First filter out invalid airport objects
      .filter(airport => 
        airport && 
        typeof airport === 'object' && 
        airport.name && 
        airport.city && 
        airport.id_airport
      )
      // Then map to select options with default values
      .map(airport => ({
        value: airport.id_airport || '',
        label: airport.name || 'Unknown Airport',
        code: airport.name || '', // Usando name como código ya que no hay un campo específico
        city: airport.city || ''
      }));
  }, [airports]);

  /**
   * Search airports function (client-side filtering)
   */
  const searchAirportsFunction = async (query: string): Promise<Airport[]> => {
    const searchTerm = query.toLowerCase().trim();
    return airports.filter(airport => 
      airport.name.toLowerCase().includes(searchTerm) ||
      airport.nombre.toLowerCase().includes(searchTerm) ||
      airport.city.toLowerCase().includes(searchTerm)
    );
  };

  return {
    airports,
    airportOptions,
    loading: isLoading,
    error: error?.message || null,
    refetch,
    searchAirports: searchAirportsFunction,
    isLoading,
    isError,
    isSuccess,
    isFetching,
  };
};

/**
 * Hook for airport search with server-side or client-side filtering
 */
export const useAirportSearch = (
  query: string, 
  options?: Omit<UseQueryOptions<Airport[], Error>, 'queryKey' | 'queryFn' | 'enabled'>
) => {
  return useQuery({
    queryKey: airportsKeys.search(query),
    queryFn: async () => {
      const airports = await fetchAirports();
      const searchTerm = query.toLowerCase().trim();
      
      return airports.filter(airport => 
        airport.name.toLowerCase().includes(searchTerm) ||
        airport.nombre.toLowerCase().includes(searchTerm) ||
        airport.city.toLowerCase().includes(searchTerm)
      );
    },
    enabled: query.length > 0, // Only search if query is not empty
    staleTime: 2 * 60 * 1000, // 2 minutes for search results
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    ...options,
  });
};

/**
 * Hook para obtener todos los aeropuertos y hacer búsqueda local (más eficiente)
 */
export const useAirportsWithLocalSearch = (
  searchQuery?: string,
  options?: Omit<UseQueryOptions<Airport[], Error>, 'queryKey' | 'queryFn'>
) => {
  const queryResult = useAirports(options);
  const { airports: allAirports } = queryResult;
  
  const filteredAirports = useMemo(() => {
    if (!searchQuery || !allAirports) return allAirports || [];
    
    const searchTerm = searchQuery.toLowerCase().trim();
    return allAirports.filter(airport => 
      airport.name.toLowerCase().includes(searchTerm) ||
      airport.nombre.toLowerCase().includes(searchTerm) ||
      airport.city.toLowerCase().includes(searchTerm)
    );
  }, [allAirports, searchQuery]);

  return {
    ...queryResult,
    data: searchQuery ? filteredAirports : allAirports,
    airports: searchQuery ? filteredAirports : allAirports,
    allAirports,
    isSearching: !!searchQuery,
    searchQuery,
  };
};
