import { useLocation, useNavigate } from 'react-router-dom';
import TopBar from '@/components/TopBar';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

// Components
import {
  BackNavigation,
  BookingDetailsHeader,
  TransferBookingCard,
  ContactInformationCard,
  FlightInformationCard,
  AdditionalItemsCard,
  CheckoutButton
} from './components';

// Hooks
import { useBookingDetailsForm } from './hooks/useBookingDetailsForm';

const BookingDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const serviceData = location.state;

  // Form management with React Hook Form
  const {
    control,
    handleSubmit,
    errors,
    isValid,
    onSubmit,
    isBookingComplete
  } = useBookingDetailsForm();

  // Navigation handlers
  const handleBackNavigation = () => {
    navigate('/booking-service');
  };

  const handleModifyBooking = () => {
    navigate('/booking-service');
  };

  // Form submission
  const handleProceedToCheckout = handleSubmit((data) => {
    const completeBookingData = onSubmit(data);

    console.log('Proceeding to checkout with complete data:', completeBookingData);

    // Aquí puedes navegar al checkout o enviar datos a la API
    // navigate('/checkout', { state: completeBookingData });
  });

  return (
    <div className="min-h-screen bg-background">
      <TopBar />
      <Header />
      
      <div className="container mx-auto px-4 py-8 md:mt-16">
        {/* Back Navigation */}
        <BackNavigation onBack={handleBackNavigation} />

        {/* Page Header */}
        <BookingDetailsHeader />

        <form onSubmit={handleProceedToCheckout} className="space-y-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Left Column - Transfer Booking Details */}
            <div className="lg:col-span-1 space-y-6">
              <TransferBookingCard
                serviceData={serviceData}
                onModifyBooking={handleModifyBooking}
              />


            </div>

            {/* Right Column - Forms */}
            <div className="lg:col-span-2 space-y-6">
              {/* Contact Information */}
              <ContactInformationCard
                control={control}
                errors={errors}
              />

              {/* Flight Information */}
              <FlightInformationCard
                control={control}
                errors={errors}
              />

              {/* Additional Items */}
              <AdditionalItemsCard
                control={control}
                errors={errors}
              />
            </div>
          </div>

          {/* Checkout Button */}
          <CheckoutButton
            onSubmit={handleProceedToCheckout}
            disabled={!isValid}
            text={isBookingComplete ? "Booking Complete!" : "Proceed to checkout"}
          />
        </form>
      </div>

      <Footer />
    </div>
  );
};

export default BookingDetails;
