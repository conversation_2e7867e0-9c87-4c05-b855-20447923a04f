import { API_ENDPOINTS, ERROR_MESSAGES } from '../../../config/config';
import { ServiceFleetRates } from '../types/service-fleet-rates.types';

export async function fetchFleetsRatesByZone(id: number): Promise<ServiceFleetRates[]> {
  const baseUrl = API_ENDPOINTS.RATES.BY_ZONE_GROUPED(id);
  try {
    const response = await fetch(baseUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const message = ERROR_MESSAGES?.SERVER || `Error fetching fleets: ${response.status} ${response.statusText}`;
      throw new Error(message);
    }

    const data = await response.json();
    if (!Array.isArray(data)) {
      throw new Error(ERROR_MESSAGES?.UNKNOWN || 'Unexpected response format');
    }
    return data as ServiceFleetRates[];
  } catch (error) {
    if (error instanceof Error) {
      // Optionally log error here
      throw new Error(error.message);
    }
    throw new Error(ERROR_MESSAGES?.UNKNOWN || 'Unknown error');
  }
}

