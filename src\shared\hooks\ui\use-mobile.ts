import { useState, useEffect } from 'react';

/**
 * Configuration constants
 */
const MOBILE_BREAKPOINT = 768;

/**
 * Hook for detecting mobile devices based on screen width
 * @returns boolean indicating if the current viewport is mobile-sized
 */
export function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined);

  useEffect(() => {
    const mediaQuery = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
    
    const handleChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };

    // Set initial value
    handleChange();
    
    // Listen for changes
    mediaQuery.addEventListener('change', handleChange);
    
    // Cleanup
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return !!isMobile;
}

/**
 * Hook for getting current viewport dimensions
 * @returns object with width, height, and mobile detection
 */
export function useViewport() {
  const [viewport, setViewport] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
    isMobile: false,
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setViewport({
        width,
        height,
        isMobile: width < MOBILE_BREAKPOINT,
      });
    };

    // Set initial value
    handleResize();
    
    // Listen for resize events
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return viewport;
}

/**
 * Hook for detecting different screen sizes
 * @returns object with boolean flags for different breakpoints
 */
export function useBreakpoints() {
  const [breakpoints, setBreakpoints] = useState({
    isMobile: false,    // < 768px
    isTablet: false,    // 768px - 1023px
    isDesktop: false,   // >= 1024px
    isLarge: false,     // >= 1280px
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      
      setBreakpoints({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
        isLarge: width >= 1280,
      });
    };

    // Set initial value
    handleResize();
    
    // Listen for resize events
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return breakpoints;
}
