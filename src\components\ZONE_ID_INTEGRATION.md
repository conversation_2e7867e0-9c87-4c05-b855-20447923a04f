# Zone ID Integration in Booking Form

## Overview

The `BookingFormHorizontal` component now properly captures and provides the `zone_id` when a hotel is selected through the hotel search functionality.

## Data Structure

When the form is submitted, the `bookingData` object now includes:

```typescript
const bookingData = {
  // Basic trip information
  roundTrip: boolean,
  from: string,
  to: string,
  date: Date,
  time: string,
  
  // Zone ID prominently available
  zone_id: number | null, // Direct access to zone ID
  
  // Detailed destination information
  destination: {
    type: 'hotel' | 'custom_address',
    zone_id: number | null,
    zone_name: string | null,
    hotel_id: number | null,
    hotel_name: string | null,
    address: string | null,
    destination_text: string
  },
  
  // Backward compatibility
  selectedHotel: Hotel | null
}
```

## Zone ID Access Methods

### 1. Direct Access
```typescript
const zoneId = bookingData.zone_id; // number | null
```

### 2. From Destination Object
```typescript
const zoneId = bookingData.destination.zone_id;
const zoneName = bookingData.destination.zone_name;
```

### 3. From Selected Hotel (Legacy)
```typescript
const zoneId = bookingData.selectedHotel?.zone_id;
```

## Usage Scenarios

### When Hotel is Selected
```typescript
// Form submission result when user selects a hotel
{
  zone_id: 3,
  destination: {
    type: 'hotel',
    zone_id: 3,
    zone_name: 'Los Cabos',
    hotel_id: 123,
    hotel_name: 'Hotel Paradise',
    address: '123 Beach Road',
    destination_text: 'Hotel Paradise - Los Cabos'
  },
  selectedHotel: {
    id: 123,
    name: 'Hotel Paradise',
    zone_id: 3,
    zone_name: 'Los Cabos',
    address: '123 Beach Road'
  }
}
```

### When Custom Address is Entered
```typescript
// Form submission result when user enters custom address
{
  zone_id: null,
  destination: {
    type: 'custom_address',
    zone_id: null,
    zone_name: null,
    hotel_id: null,
    hotel_name: null,
    address: 'Custom Address 456',
    destination_text: 'Custom Address 456'
  },
  selectedHotel: null
}
```

## Helper Functions

The component includes helper functions for better data handling:

### `getDestinationZoneId()`
Returns the zone ID of the selected destination or null.

### `getDestinationInfo()`
Returns complete destination information including type detection.

## Console Logging

The form now provides detailed console logging for debugging:

```
=== DESTINATION INFO ===
Zone ID: 3
Destination Type: hotel
Destination Details: { type: 'hotel', zone_id: 3, ... }
✅ Zone ID disponible: 3
🏨 Hotel seleccionado: { hotel_id: 123, hotel_name: 'Hotel Paradise', zone_name: 'Los Cabos' }
========================
```

## Integration with Backend

When sending to your backend API, you can access the zone ID easily:

```typescript
const handleFormSubmit = (bookingData) => {
  const apiPayload = {
    from_airport: bookingData.from,
    destination: bookingData.to,
    zone_id: bookingData.zone_id, // Direct access
    hotel_id: bookingData.destination.hotel_id,
    date: bookingData.date,
    time: bookingData.time,
    round_trip: bookingData.roundTrip
  };
  
  // Send to your booking API
  await fetch('/api/bookings', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiPayload)
  });
};
```

## Benefits

1. **Direct Zone Access** - `zone_id` is available at the top level
2. **Type Safety** - Full TypeScript support for all data structures
3. **Backward Compatibility** - Previous `selectedHotel` structure maintained
4. **Rich Information** - Detailed destination metadata available
5. **Debug Support** - Comprehensive logging for troubleshooting

## Error Handling

- Returns `null` for `zone_id` when no hotel is selected
- Gracefully handles custom addresses
- Maintains form validation for required fields
- Provides clear logging for debugging scenarios
