import { HotelSelectSearch } from '@/features/hotels';
import type { Hotel } from '@/features/hotels';

interface DestinationSearchProps {
  value: string;
  onChange: (value: string, hotel?: Hotel) => void;
  error?: boolean;
  className?: string;
  placeholder?: string;
  errorText?: string;
  required?: boolean;
  // New props for filtering
  destinationZoneId?: number;
  availableHotels?: Hotel[];
  // New prop for controlling search mode
  searchMode?: 'search' | 'dropdown' | 'both';
  // Add selectedHotel prop to pass the complete hotel object
  selectedHotel?: Hotel;
}

export const DestinationSearch = ({
  value,
  onChange,
  error,
  className,
  placeholder = "Search hotels or enter address",
  errorText,
  destinationZoneId,
  availableHotels,
  searchMode = 'both',
  selectedHotel
}: DestinationSearchProps) => {
  return (
    <div className="w-full">      
      <div className="relative">
        <HotelSelectSearch
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className={error ? "border-red-500" : className}
          error={error}
          destinationZoneId={destinationZoneId}
          availableHotels={availableHotels}
          searchMode={searchMode}
          selectedHotel={selectedHotel}
        />
      </div>
      {/* Reserve space for error message to prevent layout shift */}
      <div className="h-5 mt-1">
        {error && <p className="text-red-500 text-xs">{errorText || "Required"}</p>}
      </div>
    </div>
  );
};
