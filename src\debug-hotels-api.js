// Debug script para probar directamente la API de hoteles
// Ejecutar en la consola del navegador en http://localhost:8082

async function debugHotelsAPI() {
  const API_BASE = 'http://localhost/bajatravel/api/v1';
  
  console.log('=== DEBUG HOTELS API ===');
  
  try {
    // Test 1: Obtener todos los hoteles
    console.log('\n1. Obteniendo todos los hoteles...');
    const allHotelsResponse = await fetch(`${API_BASE}/hotels`);
    const allHotelsData = await allHotelsResponse.json();
    
    console.log('Respuesta todos los hoteles:', {
      success: allHotelsData.success,
      total: allHotelsData.total_hotels,
      count: allHotelsData.hotels?.length,
      status: allHotelsResponse.status
    });
    
    // Buscar hotels con "Corazon" en todos los hoteles
    const allCorazonHotels = allHotelsData.hotels?.filter(hotel => 
      hotel.name.toLowerCase().includes('corazon')
    ) || [];
    
    console.log('Hoteles con "Corazon" en todos los hoteles:', allCorazonHotels);
    
    // Test 2: Buscar específicamente "Corazon"
    console.log('\n2. Buscando "Corazon"...');
    const searchUrl = `${API_BASE}/hotels?search=Corazon`;
    console.log('URL de búsqueda:', searchUrl);
    
    const searchResponse = await fetch(searchUrl);
    const searchData = await searchResponse.json();
    
    console.log('Respuesta búsqueda "Corazon":', {
      success: searchData.success,
      total: searchData.total_hotels,
      count: searchData.hotels?.length,
      status: searchResponse.status,
      hotels: searchData.hotels
    });
    
    // Test 3: Buscar con diferentes variaciones
    const searchTerms = ['corazon', 'Corazon', 'CORAZON', 'Corazón'];
    
    for (const term of searchTerms) {
      console.log(`\n3.${searchTerms.indexOf(term) + 1} Buscando "${term}"...`);
      const varResponse = await fetch(`${API_BASE}/hotels?search=${encodeURIComponent(term)}`);
      const varData = await varResponse.json();
      
      console.log(`Resultado para "${term}":`, {
        success: varData.success,
        total: varData.total_hotels,
        count: varData.hotels?.length,
        hotels: varData.hotels?.map(h => ({ id: h.id, name: h.name }))
      });
    }
    
    // Test 4: Verificar status del API
    console.log('\n4. Estado del API...');
    console.log('Response headers:', Object.fromEntries(allHotelsResponse.headers.entries()));
    
  } catch (error) {
    console.error('Error en debug:', error);
  }
  
  console.log('\n=== FIN DEBUG ===');
}

// Ejecutar automáticamente
debugHotelsAPI();

// También exponer la función globalmente para poder llamarla manualmente
window.debugHotelsAPI = debugHotelsAPI;
