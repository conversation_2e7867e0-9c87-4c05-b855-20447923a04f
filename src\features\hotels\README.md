# Hotels Feature

This feature provides a complete implementation for managing hotel data in the BajaTravel Shuttle application. It includes TypeScript types, services for API communication, React Query hooks for state management, and example components.

## Features

- ✅ **Complete TypeScript Types** - Full type safety for all hotel-related data
- ✅ **Robust API Service** - HTTP client with error handling, validation, and retry logic
- ✅ **React Query Integration** - Optimized caching, background updates, and state management
- ✅ **Input Validation** - Client-side validation for all parameters
- ✅ **Error Handling** - Comprehensive error handling with user-friendly messages
- ✅ **Search & Filtering** - Debounced search with multiple filter options
- ✅ **Pagination Support** - Built-in pagination with next/previous page helpers
- ✅ **Zone-based Filtering** - Get hotels by specific zones
- ✅ **Cache Management** - Prefetching, invalidation, and manual cache control

## Project Structure

```
src/features/hotels/
├── components/
│   └── HotelsExample.tsx        # Example implementation
├── hooks/
│   └── userHotels.ts           # React Query hooks
├── services/
│   └── hotels.service.ts       # API service layer
├── types/
│   └── hotels.types.ts         # TypeScript definitions
├── index.ts                    # Main exports
└── README.md                   # This file
```

## API Endpoints

The service connects to these endpoints defined in `config.ts`:

- `GET /api/v1/hotels` - Get all hotels with optional filters
- `GET /api/v1/hotels/zone/{zoneId}` - Get hotels by zone ID

### Supported Query Parameters

| Parameter   | Type     | Description                           |
|-------------|----------|---------------------------------------|
| `page`      | number   | Page number (default: 1)             |
| `limit`     | number   | Items per page (max: 100)            |
| `zone_id`   | number   | Filter by zone ID                     |
| `search`    | string   | Search in hotel names                 |
| `status`    | string   | 'active', 'inactive', or 'all'       |
| `sort_by`   | string   | 'name', 'zone_name', 'rating', 'created_at' |
| `sort_order`| string   | 'asc' or 'desc'                      |

## Usage Examples

### Basic Hotel List

```typescript
import { useHotels } from '@/features/hotels';

function HotelsList() {
  const { hotels, isLoading, isError, error } = useHotels({
    page: 1,
    limit: 10,
    status: 'active'
  });

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error: {error?.message}</div>;

  return (
    <div>
      {hotels.map(hotel => (
        <div key={hotel.id}>{hotel.name}</div>
      ))}
    </div>
  );
}
```

### Hotels by Zone

```typescript
import { useHotelsByZone } from '@/features/hotels';

function ZoneHotels({ zoneId }: { zoneId: number }) {
  const { hotels, zoneName, totalHotels, isLoading } = useHotelsByZone({
    zone_id: zoneId,
    status: 'active'
  });

  if (isLoading) return <div>Loading zone hotels...</div>;

  return (
    <div>
      <h2>{zoneName} ({totalHotels} hotels)</h2>
      {hotels.map(hotel => (
        <div key={hotel.id}>{hotel.name}</div>
      ))}
    </div>
  );
}
```

### Search with Debouncing

```typescript
import { useHotelsSearch } from '@/features/hotels';

function HotelsSearch() {
  const {
    hotels,
    searchParams,
    updateSearch,
    resetSearch,
    isSearching,
    isLoading
  } = useHotelsSearch({ status: 'active' }, 300);

  return (
    <div>
      <input
        type="text"
        placeholder="Search hotels..."
        value={searchParams.search || ''}
        onChange={(e) => updateSearch({ search: e.target.value })}
      />
      {isSearching && <div>Searching...</div>}
      {isLoading && <div>Loading...</div>}
      
      <div>
        {hotels.map(hotel => (
          <div key={hotel.id}>{hotel.name}</div>
        ))}
      </div>
      
      <button onClick={resetSearch}>Reset Search</button>
    </div>
  );
}
```

### Cache Management

```typescript
import { useHotelsCache, usePrefetchHotels } from '@/features/hotels';

function HotelsManager() {
  const { invalidateAllHotels, removeHotelsCache } = useHotelsCache();
  const { prefetchAllHotels } = usePrefetchHotels();

  const handleRefresh = () => {
    invalidateAllHotels(); // Invalidate cache and refetch
  };

  const handleClearCache = () => {
    removeHotelsCache(); // Remove all cached data
  };

  const handlePrefetch = () => {
    prefetchAllHotels({ page: 2 }); // Prefetch next page
  };

  return (
    <div>
      <button onClick={handleRefresh}>Refresh Data</button>
      <button onClick={handleClearCache}>Clear Cache</button>
      <button onClick={handlePrefetch}>Prefetch Next Page</button>
    </div>
  );
}
```

## Type Definitions

### Hotel Interface

```typescript
interface Hotel {
  id: number;
  name: string;
  zone_id: number;
  zone_name?: string;
  address?: string;
  description?: string;
  amenities?: string[];
  rating?: number;
  image_url?: string;
  contact_info?: {
    phone?: string;
    email?: string;
    website?: string;
  };
  location?: {
    latitude?: number;
    longitude?: number;
  };
  created_at?: string;
  updated_at?: string;
  status?: 'active' | 'inactive';
}
```

### Query Parameters

```typescript
interface HotelsQueryParams {
  page?: number;
  limit?: number;
  zone_id?: number;
  search?: string;
  status?: 'active' | 'inactive' | 'all';
  sort_by?: 'name' | 'zone_name' | 'rating' | 'created_at';
  sort_order?: 'asc' | 'desc';
}
```

## Error Handling

The service provides comprehensive error handling:

- **Network Errors** - Connection timeouts and network failures
- **Validation Errors** - Invalid parameters or malformed requests
- **Server Errors** - HTTP status codes and API error responses
- **Timeout Handling** - Configurable request timeouts
- **Retry Logic** - Automatic retry for transient failures

## Validation

All inputs are validated both client-side and server-side:

- **Page Numbers** - Must be positive integers
- **Limits** - Between 1 and 100
- **Zone IDs** - Must be positive integers
- **Search Terms** - Length between 1 and 100 characters
- **Sort Parameters** - Must be from predefined lists

## Cache Strategy

The hooks use React Query for intelligent caching:

- **Background Updates** - Data is refetched in the background
- **Stale-While-Revalidate** - Shows cached data while fetching updates
- **Cache Keys** - Hierarchical keys for precise invalidation
- **Prefetching** - Anticipatory data loading for better UX

## Configuration

Caching behavior can be configured in `config.ts`:

```typescript
export const CACHE_CONFIG = {
  EXPIRATION: {
    SHORT: 5 * 60 * 1000,    // 5 minutes
    MEDIUM: 30 * 60 * 1000,  // 30 minutes (hotels default)
    LONG: 2 * 60 * 60 * 1000, // 2 hours
  },
};
```

## Testing

When testing components that use these hooks, wrap them with React Query's QueryClient:

```typescript
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render } from '@testing-library/react';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const wrapper = ({ children }) => (
  <QueryClientProvider client={queryClient}>
    {children}
  </QueryClientProvider>
);

render(<YourComponent />, { wrapper });
```

## Requirements

- React 18+
- @tanstack/react-query v5+
- TypeScript 4.7+

## Contributing

When extending this feature:

1. Update type definitions in `hotels.types.ts`
2. Add service methods in `hotels.service.ts`
3. Create corresponding hooks in `userHotels.ts`
4. Update the example component to demonstrate new functionality
5. Add tests for new functionality
