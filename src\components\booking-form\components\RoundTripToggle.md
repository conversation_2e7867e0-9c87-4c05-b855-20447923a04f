# RoundTripToggle Component

## Descripción
El componente `RoundTripToggle` permite a los usuarios alternar entre viajes de ida y vuelta (Round Trip) y viajes de ida solamente (One Way). Incluye un modal de confirmación que se muestra cuando el usuario intenta cambiar de Round Trip a One Way, destacando los beneficios de mantener el viaje de ida y vuelta.

## Características

### Modal de Confirmación
- **Trigger**: Se activa cuando el usuario intenta cambiar de Round Trip (true) a One Way (false)
- **Contenido**: Mensaje persuasivo sobre los beneficios del transporte de ida y vuelta
- **Botones**: 
  - "Change to One Way" (rojo) - Confirma el cambio
  - "Keep Round Trip" (azul cabo) - Mantiene la selección actual

### Funcionalidad
- **Cambio directo**: De One Way a Round Trip se realiza sin confirmación
- **Confirmación requerida**: De Round Trip a One Way muestra el modal
- **Diseño responsivo**: Se adapta a diferentes tamaños de pantalla

## Uso

```tsx
import { RoundTripToggle } from '@/components/booking-form/components';

<RoundTripToggle
  value={isRoundTrip}
  onChange={(value) => setIsRoundTrip(value)}
  label="Round Trip"
  className="custom-class"
/>
```

## Props

| Prop | Tipo | Requerido | Default | Descripción |
|------|------|-----------|---------|-------------|
| `value` | `boolean` | ✅ | - | Estado actual del toggle (true = Round Trip, false = One Way) |
| `onChange` | `(value: boolean) => void` | ✅ | - | Función callback cuando cambia el valor |
| `label` | `string` | ❌ | "Round Trip" | Texto del label |
| `className` | `string` | ❌ | - | Clases CSS adicionales |

## Dependencias
- `@/components/ui/switch` - Componente switch base
- `@/components/ui/button` - Botones del modal
- `@/components/ui/dialog` - Sistema de modal
- `lucide-react` - Icono AlertTriangle

## Estilos
- Utiliza las clases de Tailwind CSS del proyecto
- Colores del tema: `cabo-blue`, `red-500/600`
- Responsive design con `sm:` breakpoints

## Comportamiento UX
1. Usuario activa Round Trip por defecto
2. Si intenta desactivar Round Trip, aparece modal informativo
3. Modal explica beneficios del transporte de ida y vuelta
4. Usuario puede confirmar el cambio o mantener Round Trip
5. Cambio de One Way a Round Trip es directo (sin modal)
