# Environment Configuration for BajaTravel Frontend
# Copy this file to .env.local and update the values for your local development

# ========================================
# Application Environment
# ========================================
# Options: development, production, test
NODE_ENV=development
REACT_APP_ENVIRONMENT=development

# ========================================
# API Configuration
# ========================================
REACT_APP_API_BASE_URL=http://localhost/bajatravel
REACT_APP_API_VERSION=v1
REACT_APP_API_TIMEOUT=10000

# ========================================
# Application Information
# ========================================
REACT_APP_NAME=BajaTravel Shuttle
REACT_APP_VERSION=2.0.0

# ========================================
# Company Information
# ========================================
REACT_APP_COMPANY_NAME=BajaTravel
REACT_APP_COMPANY_PHONE=+52 **************
REACT_APP_COMPANY_EMAIL=<EMAIL>

# ========================================
# Application Defaults
# ========================================
REACT_APP_DEFAULT_CURRENCY=USD
REACT_APP_DEFAULT_LANGUAGE=en
REACT_APP_DEFAULT_ZONE_ID=1

# ========================================
# Feature Flags
# ========================================
REACT_APP_ENABLE_BOOKING=true
REACT_APP_ENABLE_PAYMENTS=false
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_DEBUG=true

# ========================================
# Cache Configuration
# ========================================
# Cache duration in milliseconds (default: 300000 = 5 minutes)
REACT_APP_CACHE_DURATION=300000

# ========================================
# Development Tools
# ========================================
# Set to true to enable various debugging features
REACT_APP_SHOW_DEV_TOOLS=true
