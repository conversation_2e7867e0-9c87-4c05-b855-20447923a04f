
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import Services from '@/components/Services';
import Fleet from '@/components/Fleet';
import Destinations from '@/components/Destinations';
import Testimonials from '@/components/Testimonials';
import Footer from '@/components/Footer';
import SEO from '@/components/SEO';
import StructuredData from '@/components/StructuredData';
import TopBar from '@/components/TopBar';
import { useAnalytics } from '@/shared/hooks/analytics';
import { useEffect } from 'react';

const Index = () => {
  const { trackPageView } = useAnalytics();

  useEffect(() => {
    trackPageView({ 
      pagePath: '/', 
      pageTitle: 'Home - Los Cabos Airport Transportation' 
    });
  }, [trackPageView]);

  return (
    <div className="min-h-screen">
      <SEO 
        title="Los Cabos Airport Transportation - Premium SJD Airport Transfers"
        description="Premium ground transportation service from Los Cabos SJD Airport to hotels. Luxury vehicles, bilingual drivers, competitive rates. Book now!"
        keywords="los cabos airport transportation, SJD transfers, cabo san lucas transportation, private transfer los cabos, airport shuttle, premium service"
      />
      <StructuredData />
      <TopBar />
      <Header />
      <Hero />
      <Services />
      <Fleet />
      <Destinations />
      <Testimonials />
      <Footer />
    </div>
  );
};

export default Index;
