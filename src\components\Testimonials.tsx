
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const Testimonials = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      location: 'Mexico City',
      rating: 5,
      comment: 'Excellent service from the airport to our resort. The driver was very friendly and gave us great recommendations for our vacation.',
      trip: 'SJD Airport → Grand Velas'
    },
    {
      name: 'John & <PERSON>',
      location: 'California, USA',
      rating: 5,
      comment: 'Outstanding service! Clean vehicle, professional driver who spoke perfect English. Made our arrival to Cabo stress-free and comfortable.',
      trip: 'SJD Airport → Cabo San Lucas Marina'
    },
    {
      name: '<PERSON>',
      location: 'Guadalajara',
      rating: 5,
      comment: 'I hired the service for my family of 6 people. The van was spacious, clean and the price was very fair. I definitely recommend them.',
      trip: 'Airport → Residences at The St. Regis'
    },
    {
      name: '<PERSON> & <PERSON>',
      location: 'Texas, USA',
      rating: 5,
      comment: 'We used their service multiple times during our stay. Always punctual, friendly service, and fair pricing. Will definitely use again!',
      trip: 'Round trip: Hotel → Airport'
    },
    {
      name: '<PERSON>',
      location: 'Monterrey',
      rating: 5,
      comment: 'Premium service at an affordable price. The vehicle had WiFi, cold water and perfect air conditioning. A very professional experience.',
      trip: 'SJD → San José del Cabo'
    },
    {
      name: 'Robert Chen',
      location: 'Vancouver, Canada',
      rating: 5,
      comment: 'Booked online easily, received confirmation quickly. Driver was waiting with our name sign, helped with luggage. Smooth and professional!',
      trip: 'Airport → One&Only Palmilla'
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="bg-cabo-coral text-white mb-4">Testimonials</Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            What Our Customers Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Thousands of travelers have trusted us for their transfers in Los Cabos. 
            Read their experiences and discover why we are the preferred choice.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="bg-white shadow-lg border-0 hover-scale">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400 mr-2">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <span key={i}>⭐</span>
                    ))}
                  </div>
                  <div className="text-sm text-gray-500">
                    {testimonial.rating}/5 stars
                  </div>
                </div>

                <blockquote className="text-gray-700 mb-4 italic leading-relaxed">
                  "{testimonial.comment}"
                </blockquote>

                <div className="border-t pt-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-semibold text-gray-900">
                        {testimonial.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {testimonial.location}
                      </div>
                    </div>
                  </div>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs border-cabo-blue text-cabo-blue">
                      {testimonial.trip}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-16">
          <div className="bg-gradient-cabo text-white rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Experience Excellence?
            </h3>
            <p className="text-xl mb-6 opacity-90">
              Join thousands of satisfied travelers who have chosen our premium service
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">🏆</span>
                <span>#1 Company in Los Cabos</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">⭐</span>
                <span>4.9/5 Average Rating</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">✅</span>
                <span>+5000 Successful Transfers</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
