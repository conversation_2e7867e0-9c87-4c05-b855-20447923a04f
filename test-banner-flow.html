<!DOCTYPE html>
<html>
<head>
    <title>Test Banner Flow</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🎯 Test: Round Trip Savings Banner Flow</h1>
    
    <div class="step info">
        <h3>📋 Test Plan</h3>
        <p>Este test verifica que el banner de descuento Round Trip funcione correctamente:</p>
        <ol>
            <li>Navegar a la página de servicios</li>
            <li>Seleccionar un servicio (hacer clic en "BOOK NOW")</li>
            <li>Verificar que el servicio se guarda en el contexto</li>
            <li>Verificar que el banner aparece en BookingDetails</li>
            <li>Verificar que el banner muestra el descuento correcto</li>
        </ol>
    </div>

    <div class="step">
        <h3>🚀 Paso 1: Abrir la aplicación</h3>
        <p>La aplicación está corriendo en: <strong>http://localhost:8081/</strong></p>
        <button onclick="window.open('http://localhost:8081/', '_blank')">Abrir Aplicación</button>
    </div>

    <div class="step">
        <h3>🏨 Paso 2: Navegar a Servicios</h3>
        <p>Desde la página principal, busca un hotel y navega a la página de servicios.</p>
        <button onclick="window.open('http://localhost:8081/booking-service', '_blank')">Ir a Servicios</button>
        <div class="code">
            URL: http://localhost:8081/booking-service
        </div>
    </div>

    <div class="step warning">
        <h3>⚠️ Paso 3: Verificar Configuración</h3>
        <p>Para que el banner funcione correctamente, asegúrate de que:</p>
        <ul>
            <li>✅ El viaje esté configurado como <strong>Round Trip</strong></li>
            <li>✅ Haya un hotel seleccionado con zone_id válido</li>
            <li>✅ Las tarifas estén disponibles en la API</li>
        </ul>
    </div>

    <div class="step">
        <h3>🚗 Paso 4: Seleccionar Servicio</h3>
        <p>En la página de servicios:</p>
        <ol>
            <li>Busca cualquier servicio (SUV, Luxury, etc.)</li>
            <li>Haz clic en el botón <strong>"BOOK NOW"</strong></li>
            <li>Esto debería navegar a BookingDetails</li>
        </ol>
    </div>

    <div class="step success">
        <h3>✅ Paso 5: Verificar Banner</h3>
        <p>En la página BookingDetails, busca el banner en la columna izquierda:</p>
        <ul>
            <li>🔍 <strong>Ubicación:</strong> Después del precio total, antes de "Free Cancellation"</li>
            <li>🎨 <strong>Apariencia:</strong> Borde verde, fondo degradado verde claro</li>
            <li>✓ <strong>Contenido:</strong> "Best Value", precio original tachado, ahorro destacado</li>
            <li>📝 <strong>Texto:</strong> "Save $X.XX (X% off) vs. booking two One-Way trips"</li>
        </ul>
    </div>

    <div class="step info">
        <h3>🔧 Debugging</h3>
        <p>Si el banner no aparece, verifica en la consola del navegador:</p>
        <div class="code">
            // Abrir DevTools (F12) y ejecutar:
            console.log('Booking Context:', window.__REACT_DEVTOOLS_GLOBAL_HOOK__);
            
            // O buscar en la consola mensajes como:
            // "Service saved to context: {...}"
            // "useServiceFleetsRates debug: {...}"
        </div>
    </div>

    <div class="step">
        <h3>📊 Resultados Esperados</h3>
        <p>Si todo funciona correctamente, deberías ver:</p>
        <ul>
            <li>✅ El servicio seleccionado se guarda en el contexto</li>
            <li>✅ El banner aparece en la ubicación correcta</li>
            <li>✅ El banner muestra un descuento real calculado</li>
            <li>✅ El diseño coincide con el mockup proporcionado</li>
        </ul>
    </div>

    <script>
        // Auto-refresh para mantener la página actualizada
        setTimeout(() => {
            location.reload();
        }, 300000); // 5 minutos
    </script>
</body>
</html>
