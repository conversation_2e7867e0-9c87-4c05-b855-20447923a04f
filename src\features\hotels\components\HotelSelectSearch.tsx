import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { useHotelsSearch } from '@/features/hotels';
import type { Hotel, HotelsQueryParams } from '@/features/hotels';
import { Search, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface HotelSelectSearchProps {
  value: string;
  onChange: (value: string, hotel?: Hotel) => void;
  placeholder?: string;
  className?: string;
  error?: boolean;
  disabled?: boolean;
  // New props for filtering
  destinationZoneId?: number;
  availableHotels?: Hotel[];
  // New prop for controlling the search mode
  searchMode?: 'search' | 'dropdown' | 'both';
  // Add selectedHotel prop to receive the selected hotel object
  selectedHotel?: Hotel;
}

export function HotelSelectSearch({
  value,
  onChange,
  placeholder = "Search or select a hotel...",
  className,
  error = false,
  disabled = false,
  destinationZoneId,
  availableHotels,
  searchMode = 'both',
  selectedHotel: initialSelectedHotel,
}: HotelSelectSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(initialSelectedHotel || null);
  const [isSearchMode, setIsSearchMode] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  
  // Use hotels search hook with debouncing - but only if no availableHotels provided and search mode enabled
  const shouldUseSearch = (!availableHotels || availableHotels.length === 0) && (searchMode === 'search' || searchMode === 'both');
  
  // Create stable search params to avoid infinite re-renders
  const searchParams = useMemo(() => {
    const params: HotelsQueryParams = {
      status: 'active',
      limit: 50,
    };
    
    if (destinationZoneId && destinationZoneId > 0) {
      params.zone_id = destinationZoneId;
    }
    
    return params;
  }, [destinationZoneId]);

  // Create stable search params with search term
  const searchParamsWithTerm = useMemo(() => {
    if (!shouldUseSearch || !searchTerm) {
      return {};
    }
    
    return {
      ...searchParams,
      search: searchTerm
    };
  }, [searchParams, searchTerm, shouldUseSearch]);

  const searchHooksResult = useHotelsSearch(
    searchParamsWithTerm,
    300
  );
  
  const {
    hotels: searchResults = [],
    isLoading: isSearching,
  } = searchHooksResult;

  // Combine available hotels with search results
  const allHotels = useMemo(() => {
    if (availableHotels && availableHotels.length > 0) {
      return availableHotels;
    }
    return searchResults;
  }, [availableHotels, searchResults]);

  // Filter hotels based on search term for local filtering
  const filteredHotels = useMemo(() => {
    if (!searchTerm) return allHotels;
    
    return allHotels.filter(hotel => 
      hotel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (hotel.zone_name && hotel.zone_name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [allHotels, searchTerm]);

  // Handle hotel selection from dropdown
  const handleHotelSelect = useCallback((hotelId: string) => {
    const hotel = allHotels.find(h => h.id.toString() === hotelId);
    if (hotel) {
      const displayName = hotel.zone_name && hotel.zone_name !== 'undefined'
        ? `${hotel.name} - ${hotel.zone_name}`
        : hotel.name;
      
      setSelectedHotel(hotel);
      setSearchTerm('');
      setIsSearchMode(false);
      setIsDropdownOpen(false);
      onChange(displayName, hotel);
    }
  }, [allHotels, onChange]);

  // Handle search input change
  const handleSearchChange = useCallback((newValue: string) => {
    setSearchTerm(newValue);
    setIsSearchMode(true);
    setIsDropdownOpen(true);

    // Only clear selected hotel if the new value is significantly different
    // from the current hotel name (not just minor edits)
    if (selectedHotel) {
      const currentHotelDisplay = selectedHotel.zone_name && selectedHotel.zone_name !== 'undefined'
        ? `${selectedHotel.name} - ${selectedHotel.zone_name}`
        : selectedHotel.name;

      // Clear hotel only if the new value doesn't contain the hotel name
      // or if the field is being cleared
      if (newValue === '' || !selectedHotel.name.toLowerCase().includes(newValue.toLowerCase())) {
        setSelectedHotel(null);
        onChange(newValue, null); // Explicitly pass null when clearing
      } else {
        // Keep the hotel but update the display value
        onChange(newValue, selectedHotel);
      }
    } else {
      onChange(newValue);
    }
  }, [selectedHotel, onChange]);

  // Handle clearing the selection
  const handleClear = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setSearchTerm('');
    setSelectedHotel(null);
    setIsSearchMode(false);
    setIsDropdownOpen(false);
    onChange('', null); // Explicitly pass null when clearing
  }, [onChange]);

  // Sync with initial selected hotel prop - FORCE UPDATE
  useEffect(() => {
    if (initialSelectedHotel) {
      setSelectedHotel(initialSelectedHotel);
      // Force the parent to know about the display value
      const displayName = initialSelectedHotel.zone_name && initialSelectedHotel.zone_name !== 'undefined'
        ? `${initialSelectedHotel.name} - ${initialSelectedHotel.zone_name}`
        : initialSelectedHotel.name;
      onChange(displayName, initialSelectedHotel);
    }
  }, [initialSelectedHotel, onChange]);

  // Set initial selected hotel if value matches
  useEffect(() => {
    if (value && !selectedHotel && allHotels.length > 0) {
      const matchingHotel = allHotels.find(hotel => {
        const displayName = hotel.zone_name && hotel.zone_name !== 'undefined'
          ? `${hotel.name} - ${hotel.zone_name}`
          : hotel.name;
        return displayName === value || hotel.name === value;
      });
      
      if (matchingHotel) {
        setSelectedHotel(matchingHotel);
      }
    }
  }, [value, selectedHotel, allHotels]);

  // Simple display value - ONE source of truth
  const displayValue = useMemo(() => {
    // 1. If we're typing, show what we're typing
    if (isSearchMode && searchTerm) {
      return searchTerm;
    }
    
    // 2. If we have a selected hotel, show it
    if (selectedHotel) {
      const display = selectedHotel.zone_name && selectedHotel.zone_name !== 'undefined'
        ? `${selectedHotel.name} - ${selectedHotel.zone_name}`
        : selectedHotel.name;
      return display;
    }
    
    // 3. Otherwise, show the value prop
    return value || '';
  }, [isSearchMode, searchTerm, selectedHotel, value]);

  return (
    <div className={cn("relative", className)}>
      {/* Search Input Mode - Only show if searchMode includes 'search' */}
      {(searchMode === 'search' || searchMode === 'both') && (
        <div className="relative">
          <Input
            type="text"
            value={displayValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            onFocus={() => {
              setIsSearchMode(true);
              setIsDropdownOpen(true);
              // Select all text when focusing to make it easier to replace
              const input = document.activeElement as HTMLInputElement;
              if (input) {
                input.select();
              }
            }}
            onBlur={() => {
              // Delay closing to allow clicking on dropdown items
              setTimeout(() => {
                setIsDropdownOpen(false);
                // Only set search mode to false if we have a selection
                if (selectedHotel) {
                  setIsSearchMode(false);
                }
              }, 150);
            }}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              "pr-10",
              error && "border-red-500 focus:border-red-500 focus:ring-red-500",
              "transition-all duration-200",
              className
            )}
          />
          
          {/* Search Icon */}
          {selectedHotel ? (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-transparent"
              onClick={handleClear}
            >
              <X className="text-white h-4 w-4 text-muted-foreground hover:text-foreground" />
            </Button>
          ) : (
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white pointer-events-none" />
          )}
        </div>
      )}

      {/* Show search dropdown if we're in search mode and dropdown is open */}
      {(searchMode === 'search' || searchMode === 'both') && isDropdownOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 max-h-60 overflow-auto rounded-md border bg-popover text-popover-foreground shadow-md">
          {isSearching && (
            <div className="flex items-center justify-center py-4 text-sm text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
              Searching hotels...
            </div>
          )}
          
          {!isSearching && filteredHotels.length === 0 && searchTerm && (
            <div className="py-4 text-center text-sm text-muted-foreground">
              No hotels found matching "{searchTerm}"
            </div>
          )}
          
          {!isSearching && filteredHotels.length === 0 && !searchTerm && allHotels.length > 0 && (
            <div className="py-2 text-center text-sm text-muted-foreground">
              Start typing to search hotels...
            </div>
          )}
          
          {filteredHotels.map((hotel) => {
            const displayName = hotel.zone_name && hotel.zone_name !== 'undefined'
              ? `${hotel.name} - ${hotel.zone_name}`
              : hotel.name;
            
            return (
              <div
                key={hotel.id}
                className="relative flex w-full cursor-pointer select-none items-center rounded-sm py-2 px-3 text-sm outline-none hover:bg-accent hover:text-accent-foreground transition-colors"
                onClick={() => handleHotelSelect(hotel.id.toString())}
              >
                <div className="flex items-center gap-3 w-full">
                  {/* Hotel image if available */}
                  {hotel.image_url && (
                    <div className="flex-shrink-0">
                      <img
                        src={hotel.image_url}
                        alt={hotel.name}
                        className="w-12 h-12 object-cover rounded-md"
                        loading="lazy"
                        onError={(e) => {
                          // Hide image if it fails to load
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    </div>
                  )}

                  <div className="flex flex-col flex-grow">
                    <span className="font-medium">{hotel.name}</span>
                    {hotel.zone_name && hotel.zone_name !== 'undefined' && (
                      <span className="text-xs text-muted-foreground">{hotel.zone_name}</span>
                    )}
                    {hotel.address && (
                      <span className="text-xs text-muted-foreground/80">{hotel.address}</span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Dropdown Select Mode - Only show if searchMode includes 'dropdown' */}
      {(searchMode === 'dropdown' || (searchMode === 'both' && !isSearchMode)) && allHotels.length > 0 && (
        <Select
          value={selectedHotel?.id.toString() || ''}
          onValueChange={handleHotelSelect}
          disabled={disabled}
        >
          <SelectTrigger className={cn(
            searchMode === 'both' ? "mt-2 h-8 text-xs" : "h-11",
            error && "border-red-500 focus:border-red-500 focus:ring-red-500"
          )}>
            <SelectValue placeholder={searchMode === 'both' ? "Or select from list" : placeholder} />
          </SelectTrigger>
          <SelectContent>
            {allHotels.slice(0, 20).map((hotel) => (
              <SelectItem key={hotel.id} value={hotel.id.toString()}>
                <div className="flex items-center gap-2">
                  {/* Hotel image if available */}
                  {hotel.image_url && (
                    <img
                      src={hotel.image_url}
                      alt={hotel.name}
                      className="w-8 h-8 object-cover rounded-sm flex-shrink-0"
                      loading="lazy"
                      onError={(e) => {
                        // Hide image if it fails to load
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  )}

                  <div className="flex flex-col">
                    <span className="font-medium">{hotel.name}</span>
                    {hotel.zone_name && hotel.zone_name !== 'undefined' && (
                      <span className="text-xs text-muted-foreground">{hotel.zone_name}</span>
                    )}
                  </div>
                </div>
              </SelectItem>
            ))}
            {allHotels.length > 20 && (
              <div className="py-2 text-center text-xs text-muted-foreground border-t">
                +{allHotels.length - 20} more hotels...
              </div>
            )}
          </SelectContent>
        </Select>
      )}
    </div>
  );
}
