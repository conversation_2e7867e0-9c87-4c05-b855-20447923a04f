import { useState, useRef, useEffect, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { useHotelsSearch } from '@/features/hotels';
import type { Hotel } from '@/features/hotels';

interface HotelSearchProps {
  value: string;
  onChange: (value: string, hotel?: Hotel) => void;
  placeholder?: string;
  className?: string;
  error?: boolean;
  disabled?: boolean;
  // New props for filtering
  destinationZoneId?: number;
  availableHotels?: Hotel[];
}

export function HotelSearch({
  value,
  onChange,
  placeholder = "Search hotels...",
  className,
  error = false,
  disabled = false,
  destinationZoneId,
  availableHotels,
}: HotelSearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null);
  
  // (Eliminado useEffect duplicado antes de rawHotels)
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Use hotels search hook with debouncing - but only if no availableHotels provided
  const shouldUseSearch = !availableHotels || availableHotels.length === 0;
  
  // Create stable search params to avoid infinite re-renders
  const searchParams = useMemo(() => {
    const params: { status: 'active' | 'inactive' | 'all'; limit: number; zone_id?: number } = {
      status: 'active',
      limit: 50, // Increased to get more results for better sorting
    };
    
    // Only add zone_id if it's a valid number
    if (destinationZoneId && destinationZoneId > 0) {
      params.zone_id = destinationZoneId;
    }
    
    return params;
  }, [destinationZoneId]);
  
  const {
    hotels: rawHotels,
    updateSearch,
    isLoading,
    isSearching,
    searchParams: currentSearchParams,
  } = useHotelsSearch(searchParams, 300);


  // Sincroniza inputValue y selectedHotel con la prop value (debe ir después de rawHotels)
  useEffect(() => {
    setInputValue(value);
    const sourceHotels = availableHotels && availableHotels.length > 0 ? availableHotels : rawHotels;
    const found = sourceHotels.find(hotel => {
      const displayName = hotel.zone_name ? `${hotel.name} - ${hotel.zone_name}` : hotel.name;
      return value === displayName;
    });
    setSelectedHotel(found || null);
  }, [value, availableHotels, rawHotels]);

  // Score and sort hotels by relevance
  const hotels = useMemo(() => {
    // Use provided hotels or fallback to search results
    const sourceHotels = availableHotels && availableHotels.length > 0 ? availableHotels : rawHotels;
    
    if (!inputValue.trim() || sourceHotels.length === 0) {
      return sourceHotels;
    }

    const searchTerm = inputValue.trim().toLowerCase();
    
    const scoredHotels = sourceHotels.map(hotel => {
      let score = 0;
      const hotelName = hotel.name.toLowerCase();
      const zoneName = (hotel.zone_name || '').toLowerCase();
      
      // Exact match gets highest score
      if (hotelName === searchTerm) {
        score += 1000;
      }
      // Starts with search term gets high score
      else if (hotelName.startsWith(searchTerm)) {
        score += 500;
      }
      // Contains search term gets medium score
      else if (hotelName.includes(searchTerm)) {
        score += 100;
        // Bonus if it's early in the name
        const index = hotelName.indexOf(searchTerm);
        score += Math.max(0, 50 - index);
      }
      
      // Zone name relevance (lower priority)
      if (zoneName.includes(searchTerm)) {
        score += 20;
      }
      
      // Bonus for shorter names (more specific)
      score += Math.max(0, 100 - hotelName.length);
      
      return { hotel, score };
    });

    // Sort by score (highest first) and take top 10
    const sortedResults = scoredHotels.sort((a, b) => b.score - a.score);
    
    // Debug logging for scoring
    if (searchTerm === 'corazon') {
      console.log('🎯 Relevance Scoring Debug:', {
        searchTerm,
        totalHotels: rawHotels.length,
        topScored: sortedResults.slice(0, 10).map(item => ({
          name: item.hotel.name,
          score: item.score,
          contains: item.hotel.name.toLowerCase().includes(searchTerm)
        }))
      });
    }
    
    return sortedResults.slice(0, 10).map(item => item.hotel);
  }, [rawHotels, availableHotels, inputValue]);

  // Debug logging para la búsqueda
  useEffect(() => {
    if (inputValue.trim().length > 0) {
      const sourceHotels = availableHotels && availableHotels.length > 0 ? availableHotels : rawHotels;
      console.log('🔍 HotelSearch Debug:', {
        searchTerm: inputValue.trim(),
        searchParams,
        sourceHotelsCount: sourceHotels.length,
        sortedHotelsCount: hotels.length,
        isLoading,
        isSearching,
        usingAvailableHotels: !!(availableHotels && availableHotels.length > 0),
        destinationZoneId,
        topHotels: hotels.slice(0, 5).map(h => ({ 
          id: h.id, 
          name: h.name, 
          zone_name: h.zone_name,
          relevanceCalculated: h.name.toLowerCase().includes(inputValue.trim().toLowerCase())
        }))
      });
    }
  }, [inputValue, hotels, availableHotels, destinationZoneId, rawHotels, isLoading, isSearching, searchParams]);

  // Update search when input changes
  useEffect(() => {
    // Only search if we're not using provided availableHotels
    if (!shouldUseSearch) {
      return;
    }

    if (inputValue.trim().length > 0) {
      // Si tenemos un hotel seleccionado y el input contiene el nombre completo,
      // buscar solo por el nombre del hotel (sin la zona)
      let searchTerm = inputValue.trim();
      
      if (selectedHotel) {
        const selectedHotelDisplay = selectedHotel.zone_name 
          ? `${selectedHotel.name} - ${selectedHotel.zone_name}`
          : selectedHotel.name;
        
        // Si el input coincide exactamente con el hotel seleccionado,
        // buscar solo por el nombre del hotel para encontrar hoteles similares
        if (inputValue === selectedHotelDisplay) {
          searchTerm = selectedHotel.name;
        }
      }
      
      updateSearch({ 
        search: searchTerm,
        sort_by: 'name', // Sort by name for better relevance
        sort_order: 'asc' // Ascending order
      });
      setIsOpen(true);
    } else {
      setIsOpen(false);
      // Clear results when input is empty
      updateSearch({});
    }
  }, [inputValue, updateSearch, selectedHotel, shouldUseSearch]);

  // Handle input focus - show dropdown with relevant results
  const handleInputFocus = () => {
    if (inputValue.trim().length > 0 || (availableHotels && availableHotels.length > 0)) {
      setIsOpen(true);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // Si el usuario está escribiendo algo diferente, limpiar la selección
    if (selectedHotel) {
      const selectedHotelDisplay = selectedHotel.zone_name 
        ? `${selectedHotel.name} - ${selectedHotel.zone_name}`
        : selectedHotel.name;
      
      // Solo limpiar si el nuevo valor es diferente al hotel seleccionado
      if (newValue !== selectedHotelDisplay && !selectedHotel.name.toLowerCase().includes(newValue.toLowerCase())) {
        setSelectedHotel(null);
      }
    }
    
    onChange(newValue);
  };

  // Handle hotel selection
  const handleHotelSelect = (hotel: Hotel) => {
    const displayName = hotel.zone_name 
      ? `${hotel.name} - ${hotel.zone_name}`
      : hotel.name;
    
    setInputValue(displayName);
    setSelectedHotel(hotel);
    setIsOpen(false);
    onChange(displayName, hotel);
    inputRef.current?.blur();
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
    }
  };

  return (
    <div ref={containerRef} className="relative">
      {/* Single input only! */}
      <Input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={handleInputFocus}
        placeholder={placeholder}
        className={cn(
          "h-12",
          error && "border-red-500",
          className
        )}
        disabled={disabled}
        autoComplete="off"
      />

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-[60] w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
          {isLoading || isSearching ? (
            <div className="p-3 text-sm text-gray-500 text-center">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
                <span>Searching hotels...</span>
              </div>
            </div>
          ) : hotels.length > 0 ? (
            <div>
              <div className="p-2 text-xs text-gray-500 border-b">
                {hotels.length} hotel{hotels.length !== 1 ? 's' : ''} found
              </div>
              {hotels.map((hotel) => (
                <button
                  key={hotel.id}
                  type="button"
                  className="w-full text-left p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 focus:bg-gray-50 focus:outline-none"
                  onClick={() => handleHotelSelect(hotel)}
                >
                  <div className="flex flex-col">
                    <span className="font-medium text-gray-900">
                      {hotel.name}
                    </span>
                    {hotel.zone_name && (
                      <span className="text-sm text-gray-500">
                        {hotel.zone_name}
                      </span>
                    )}
                    {hotel.address && (
                      <span className="text-xs text-gray-400 mt-1">
                        {hotel.address}
                      </span>
                    )}
                  </div>
                </button>
              ))}
            </div>
          ) : inputValue.trim().length > 0 ? (
            <div className="p-4 text-sm text-gray-500 text-center">
              <div className="mb-2">
                No hotels found matching "{inputValue.trim().length > 30 ? inputValue.trim().substring(0, 30) + '...' : inputValue.trim()}"
              </div>
              <div className="text-xs text-gray-400">
                {selectedHotel ? 'Try typing a different hotel name' : 'You can still enter a custom address'}
              </div>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}

export default HotelSearch;
