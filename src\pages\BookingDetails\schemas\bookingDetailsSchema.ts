import { z } from 'zod';

// Schema para información de contacto
export const contactInfoSchema = z.object({
  firstName: z.string().min(1, 'First name is required').trim(),
  lastName: z.string().min(1, 'Last name is required').trim(),
  email: z.string().email('Please enter a valid email address').trim(),
  confirmEmail: z.string().email('Please enter a valid email address').trim(),
  phone: z.string().min(1, 'Phone number is required').trim(),
  countryCode: z.string().default('US')
}).refine((data) => data.email === data.confirmEmail, {
  message: "Emails don't match",
  path: ["confirmEmail"]
});

// Schema para información de vuelo (opcional)
export const flightInfoSchema = z.object({
  arrivalTime: z.string().optional(),
  airline: z.string().optional(),
  flightNumber: z.string().optional()
});

// Schema para servicios adicionales
export const extraServicesSchema = z.object({
  stopShop: z.boolean().default(false),
  golfBags: z.boolean().default(false),
  surfboards: z.boolean().default(false)
});

// Schema para servicios adicionales
export const additionalItemsSchema = z.object({
  babySeat: z.string().default('0'),
  carSeat: z.string().default('0'),
  boosterSeat: z.string().default('0'),
  specialInstructions: z.string().optional().default(''),
  extraServices: extraServicesSchema
});

// Schema completo del formulario de detalles de reserva
export const bookingDetailsSchema = z.object({
  contactInfo: contactInfoSchema,
  flightInfo: flightInfoSchema,
  additionalItems: additionalItemsSchema
});

// Tipos inferidos de los schemas
export type ContactInfoFormData = z.infer<typeof contactInfoSchema>;
export type FlightInfoFormData = z.infer<typeof flightInfoSchema>;
export type AdditionalItemsFormData = z.infer<typeof additionalItemsSchema>;
export type BookingDetailsFormData = z.infer<typeof bookingDetailsSchema>;

// Valores por defecto
export const defaultContactInfo: ContactInfoFormData = {
  firstName: '',
  lastName: '',
  email: '',
  confirmEmail: '',
  phone: '',
  countryCode: 'US'
};

export const defaultFlightInfo: FlightInfoFormData = {
  arrivalTime: '',
  airline: '',
  flightNumber: ''
};

export const defaultAdditionalItems: AdditionalItemsFormData = {
  babySeat: '0',
  carSeat: '0',
  boosterSeat: '0',
  specialInstructions: '',
  extraServices: {
    stopShop: false,
    golfBags: false,
    surfboards: false
  }
};

export const defaultBookingDetailsData: BookingDetailsFormData = {
  contactInfo: defaultContactInfo,
  flightInfo: defaultFlightInfo,
  additionalItems: defaultAdditionalItems
};
